"""
CogBridges Search - 核心业务流程服务
实现完整的串行业务流程：Google搜索 -> Reddit帖子获取 -> 评论者历史数据获取
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field

from config import config
from services.google_search_api import GoogleSearchService
from services.reddit_service import RedditService
from services.data_service import DataService
from services.llm_service import llm_service

from models.reddit_models import RedditPost, RedditComment
from models.search_models import SearchQuery, GoogleSearchResult, SearchResult
from utils.logger_utils import get_logger
# from utils.performance_monitor import performance_monitor, PerformanceContext


@dataclass
class CogBridgesSearchResult:
    """CogBridges搜索结果"""
    query: str  # 原始查询
    session_id: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 翻译信息
    translated_query: str = ""  # 翻译后的查询
    translation_time: float = 0.0  # 翻译耗时
    
    # 步骤1: Google搜索结果
    google_results: List[Dict[str, Any]] = field(default_factory=list)
    google_search_time: float = 0.0
    
    # 步骤2: Reddit帖子数据
    reddit_posts: List[Dict[str, Any]] = field(default_factory=list)
    reddit_posts_time: float = 0.0
    
    # 步骤3: 评论者历史数据
    commenters_history: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    commenters_history_time: float = 0.0
    
    # 步骤4: LLM分析结果（新增）
    llm_analysis: Dict[str, Any] = field(default_factory=dict)
    llm_analysis_time: float = 0.0
    
    # 总体统计
    total_time: float = 0.0
    success: bool = True
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query,
            "translated_query": self.translated_query,
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "translation_time": self.translation_time,
            "google_results": self.google_results,
            "google_search_time": self.google_search_time,
            "reddit_posts": self.reddit_posts,
            "reddit_posts_time": self.reddit_posts_time,
            "commenters_history": self.commenters_history,
            "commenters_history_time": self.commenters_history_time,
            "llm_analysis": self.llm_analysis,
            "llm_analysis_time": self.llm_analysis_time,
            "total_time": self.total_time,
            "success": self.success,
            "error_message": self.error_message
        }


class CogBridgesService:
    """CogBridges核心业务服务"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = get_logger(__name__)
        
        # 初始化子服务
        self.google_service = GoogleSearchService()
        self.reddit_service = RedditService()  # 直接实例化
        self.data_service = DataService()
        self.llm_service = llm_service

        
        # 业务参数（可配置）
        self.max_search_results = 5  # 前5个搜索结果
        self.max_comments_per_post = 6  # 每个帖子前6个评论
        self.max_user_comments = 20  # 用户历史前20个评论
        self.max_user_posts = 10  # 用户历史前10个帖子
        
        self.logger.info("CogBridges核心业务服务初始化成功")



    async def close(self):
        """关闭服务并清理资源"""
        await self.reddit_service.close()
        self.logger.info("CogBridges服务已关闭")
    
    async def search(self, query: str, save_to_db: bool = True) -> CogBridgesSearchResult:
        """
        执行完整的CogBridges搜索流程

        Args:
            query: 搜索查询
            save_to_db: 是否保存到数据库，默认为True

        Returns:
            完整的搜索结果
        """
        start_time = time.time()
        session_id = self.data_service.generate_session_id(query)
        
        result = CogBridgesSearchResult(
            query=query,
            session_id=session_id
        )
        
        try:
            self.logger.info(f"开始CogBridges搜索流程: {query}")
            
            # 步骤0: 智能搜索策略生成（如果LLM服务可用）
            search_queries = [query]  # 默认使用原查询
            optimization_time = 0.0

            if self.llm_service and self.llm_service.configured:
                try:
                    self.logger.info(f"步骤0: 生成智能搜索策略 - {query}")
                    optimization_start = time.time()

                    # 生成多个智能搜索查询
                    search_queries = await self.llm_service.generate_intelligent_search_queries(query)
                    optimization_time = time.time() - optimization_start

                    self.logger.info(f"步骤0完成: 生成了 {len(search_queries)} 个搜索策略, 耗时: {optimization_time:.2f}秒")
                    self.logger.info(f"搜索策略: {search_queries}")

                except Exception as e:
                    self.logger.warning(f"智能搜索策略生成失败，使用原查询: {e}")
                    search_queries = [query]

            # 保存优化信息到结果中
            result.translated_query = search_queries[0] if search_queries else query  # 使用第一个查询作为主查询
            result.translation_time = optimization_time

            # 步骤1: 智能Google搜索（尝试多个查询策略）
            google_results = await self._step1_intelligent_google_search(search_queries)
            result.google_results = google_results["results"]
            result.google_search_time = google_results["search_time"]

            if not result.google_results:
                result.success = False
                result.error_message = "Google搜索未找到结果"
                return result

            # 步骤2: 并行获取Reddit帖子内容和评论
            reddit_data = await self._step2_get_reddit_posts(result.google_results)
            result.reddit_posts = reddit_data["posts"]
            result.reddit_posts_time = reddit_data["processing_time"]

            if not result.reddit_posts:
                result.success = False
                result.error_message = "未能获取Reddit帖子数据"
                return result

            # 步骤3: 并行获取评论者历史数据
            commenters_data = await self._step3_get_commenters_history(result.reddit_posts)
            result.commenters_history = commenters_data["history"]
            result.commenters_history_time = commenters_data["processing_time"]
            
            # 步骤4: LLM分析（如果LLM服务可用且有评论者数据）
            llm_analysis_results = None
            llm_analysis_time = 0.0
            
            if (self.llm_service and self.llm_service.configured and 
                result.commenters_history and len(result.commenters_history) > 0):
                try:
                    self.logger.info(f"步骤4: 开始LLM分析...")
                    llm_analysis_start = time.time()
                    
                    # 执行LLM分析
                    llm_analysis_results = await self._step4_llm_analysis(result)
                    
                    llm_analysis_time = time.time() - llm_analysis_start
                    self.logger.info(f"步骤4完成: LLM分析完成, 耗时: {llm_analysis_time:.2f}秒")
                    
                except Exception as e:
                    self.logger.warning(f"LLM分析失败: {e}")
                    llm_analysis_results = {"error": str(e)}
            
            # 保存LLM分析结果
            result.llm_analysis = llm_analysis_results or {}
            result.llm_analysis_time = llm_analysis_time
            
            # 计算总时间
            result.total_time = time.time() - start_time

            # 根据参数决定是否保存完整结果
            if save_to_db:
                await self._save_results(result)

            self.logger.info(f"CogBridges搜索完成: {query}, 总耗时: {result.total_time:.2f}秒")
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            result.total_time = time.time() - start_time
            self.logger.error(f"CogBridges搜索失败: {e}")
        
        return result

    async def save_search_result_async(self, result: CogBridgesSearchResult):
        """
        异步保存搜索结果到数据库

        Args:
            result: 搜索结果对象
        """
        try:
            await self._save_results(result)
            self.logger.info(f"搜索结果异步保存成功: {result.session_id}")
        except Exception as e:
            self.logger.error(f"搜索结果异步保存失败: {e}")


    async def _step1_google_search(self, query: str) -> Dict[str, Any]:
        """步骤1: 使用Google API搜索内容"""
        self.logger.info(f"步骤1: Google搜索 - {query}")
        start_time = time.time()
        
        search_result = await self.google_service.search(
            query=query,
            max_results=self.max_search_results
        )
        
        if not search_result.success:
            self.logger.warning(f"Google搜索失败: {search_result.error_message}")
            return {"results": [], "search_time": time.time() - start_time}
        
        # 直接使用API返回的结果
        formatted_results = [
            {
                "title": res.title,
                "url": res.url,
                "snippet": res.snippet,
                "rank": res.rank
            }
            for res in search_result.results
        ]
        
        search_time = time.time() - start_time
        self.logger.info(f"步骤1完成: 找到 {len(formatted_results)} 个结果, 耗时: {search_time:.2f}秒")
        
        return {
            "results": formatted_results,
            "search_time": search_time
        }

    async def _step1_intelligent_google_search(self, search_queries: List[str]) -> Dict[str, Any]:
        """步骤1: 智能Google搜索 - 尝试多个查询策略并选择最佳结果"""
        self.logger.info(f"步骤1: 智能Google搜索 - 尝试 {len(search_queries)} 个查询策略")
        start_time = time.time()

        all_results = []
        best_results = []
        best_relevance_score = 0.0
        total_search_time = 0.0

        for i, query in enumerate(search_queries):
            try:
                self.logger.info(f"尝试搜索策略 {i+1}/{len(search_queries)}: {query}")

                # 执行搜索
                search_result = await self.google_service.search(
                    query=query,
                    max_results=self.max_search_results
                )

                if not search_result.success:
                    self.logger.warning(f"搜索策略 {i+1} 失败: {search_result.error_message}")
                    continue

                # 格式化结果
                formatted_results = [
                    {
                        "title": res.title,
                        "url": res.url,
                        "snippet": res.snippet,
                        "rank": res.rank,
                        "search_query": query  # 记录使用的搜索查询
                    }
                    for res in search_result.results
                ]

                all_results.extend(formatted_results)

                # 如果有LLM服务，评估结果相关性
                if self.llm_service and self.llm_service.configured and formatted_results:
                    try:
                        evaluation = await self.llm_service.evaluate_search_results_relevance(
                            search_queries[0],  # 使用原始查询进行评估
                            formatted_results
                        )

                        relevance_score = evaluation.get("relevance_score", 0.5)
                        self.logger.info(f"搜索策略 {i+1} 相关性评分: {relevance_score}")

                        # 如果这个策略的结果更相关，更新最佳结果
                        if relevance_score > best_relevance_score:
                            best_relevance_score = relevance_score
                            best_results = formatted_results
                            self.logger.info(f"更新最佳搜索结果 (相关性: {relevance_score})")

                        # 如果相关性已经很高，可以提前结束
                        if relevance_score >= 0.8:
                            self.logger.info(f"找到高相关性结果 ({relevance_score})，提前结束搜索")
                            break

                    except Exception as e:
                        self.logger.warning(f"评估搜索策略 {i+1} 相关性失败: {e}")
                        # 如果评估失败，但这是第一个有结果的策略，就使用它
                        if not best_results:
                            best_results = formatted_results
                else:
                    # 如果没有LLM服务，使用第一个有结果的策略
                    if not best_results:
                        best_results = formatted_results

            except Exception as e:
                self.logger.error(f"搜索策略 {i+1} 执行失败: {e}")
                continue

        total_search_time = time.time() - start_time

        # 如果没有找到最佳结果，使用所有结果的前几个
        if not best_results and all_results:
            best_results = all_results[:self.max_search_results]
            self.logger.info("未找到最佳结果，使用所有结果的前几个")

        # 去重（基于URL）
        seen_urls = set()
        unique_results = []
        for result in best_results:
            if result["url"] not in seen_urls:
                seen_urls.add(result["url"])
                unique_results.append(result)

        self.logger.info(f"步骤1完成: 智能搜索找到 {len(unique_results)} 个唯一结果，"
                        f"最佳相关性: {best_relevance_score:.2f}，耗时 {total_search_time:.2f}秒")

        return {
            "results": unique_results,
            "search_time": total_search_time,
            "best_relevance_score": best_relevance_score,
            "total_strategies_tried": len(search_queries)
        }

    async def _step2_get_reddit_posts(self, google_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤2: 并行获取Reddit帖子内容和评论"""
        self.logger.info(f"步骤2: 并行获取 {len(google_results)} 个Reddit帖子的内容和评论")
        start_time = time.time()
        
        # 创建并行任务
        tasks = []
        for result in google_results:
            task = self._get_single_post_data(result["url"], result)
            tasks.append(task)
        
        # 并行执行
        posts_data = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        valid_posts = []
        for data in posts_data:
            if isinstance(data, dict) and data.get("success"):
                valid_posts.append(data)
            elif isinstance(data, Exception):
                self.logger.warning(f"获取帖子数据失败: {data}")
        
        processing_time = time.time() - start_time
        self.logger.info(f"步骤2完成: 成功获取 {len(valid_posts)} 个帖子数据, 耗时: {processing_time:.2f}秒")
        
        return {
            "posts": valid_posts,
            "processing_time": processing_time
        }
    
    async def _get_single_post_data(self, url: str, google_result: Dict[str, Any]) -> Dict[str, Any]:
        """获取单个帖子的完整数据"""
        try:
            # 获取帖子详情
            post_details = await self.reddit_service.get_post_details(url)
            if not post_details:
                return {"success": False, "error": "帖子不存在"}
            
            # 获取评论
            comments = await self.reddit_service.get_post_comments(
                url, 
                limit=self.max_comments_per_post
            )
            
            return {
                "success": True,
                "google_result": google_result,
                "post": {
                    "id": post_details["id"],
                    "title": post_details["title"],
                    "author": post_details["author"],
                    "score": post_details["score"],
                    "num_comments": post_details["num_comments"],
                    "subreddit": post_details["subreddit"],
                    "url": post_details["url"],
                    "created_utc": post_details["created_utc"],
                    "selftext": post_details.get("selftext", "")
                },
                "comments": [
                    {
                        "id": c["id"],
                        "author": c["author"],
                        "body": c["body"],
                        "score": c["score"],
                        "created_utc": c["created_utc"]
                    } for c in comments
                ],
                "commenters": list(set(c["author"] for c in comments if c["author"] != "[deleted]"))
            }
            
        except Exception as e:
            self.logger.warning(f"获取帖子数据失败 {url}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _step3_get_commenters_history(self, posts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤3: 使用新的overview获取方法获取评论者历史数据"""
        self.logger.info("步骤3: 使用新的overview方法获取评论者历史数据")
        start_time = time.time()

        # 收集所有评论者
        all_commenters = set()
        for post_data in posts_data:
            all_commenters.update(post_data["commenters"])

        # 过滤有效评论者
        filtered_commenters = self._filter_valid_commenters(list(all_commenters))
        self.logger.info(f"需要获取 {len(filtered_commenters)} 个评论者的历史数据 (原始: {len(all_commenters)})")

        # 使用新的overview获取方法
        commenters_history = {}
        max_items_per_user = 100  # 每个用户最多100条记录

        # 创建并发任务
        semaphore = asyncio.Semaphore(len(filtered_commenters))  # 最大并发数等于用户数
        
        async def fetch_user_overview(username):
            async with semaphore:
                return await self.reddit_service.get_user_full_overview_history(username, max_items_per_user)

        # 并发执行所有任务
        tasks = [fetch_user_overview(username) for username in filtered_commenters]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(results):
            username = filtered_commenters[i]
            if isinstance(result, dict) and result.get('status') == 'success':
                # 转换格式以兼容原有结构
                commenters_history[username] = self._convert_overview_to_legacy_format(result)
            elif isinstance(result, Exception):
                self.logger.warning(f"获取用户 {username} 历史失败: {result}")

        processing_time = time.time() - start_time
        self.logger.info(f"步骤3完成: 获取了 {len(commenters_history)} 个用户的历史数据, 耗时: {processing_time:.2f}秒")

        return {
            "history": commenters_history,
            "processing_time": processing_time
        }
    

    
    async def _save_results(self, result: CogBridgesSearchResult):
        """保存完整的搜索结果"""
        try:
            # 构建完整的会话数据
            complete_data = {
                "session_id": result.session_id,
                "timestamp": result.timestamp.isoformat(),
                "search_result": result.to_dict(),
                "metadata": {
                    "service_version": "2.0.0",
                    "features_enabled": {
                        "llm_analysis": bool(result.llm_analysis),
                        "translation": bool(result.translated_query and result.translated_query != result.query),
                        "enhanced_comments": True
                    }
                },
                "statistics": {
                    "google_results_count": len(result.google_results) if result.google_results else 0,
                    "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                    "commenters_count": len(result.commenters_history) if result.commenters_history else 0,
                    "similarity_analysis_count": len(result.llm_analysis.get("similarity_analysis", {})) if result.llm_analysis else 0,
                    "motivation_analysis_count": sum(len(motivations) for motivations in result.llm_analysis.get("motivation_analysis", {}).values()) if result.llm_analysis else 0,
                    "translation_time": result.translation_time,
                    "google_search_time": result.google_search_time,
                    "reddit_posts_time": result.reddit_posts_time,
                    "commenters_history_time": result.commenters_history_time,
                    "llm_analysis_time": result.llm_analysis_time,
                    "total_time": result.total_time
                },
                "business_flow_results": self._build_business_flow_results(result),
                "llm_analysis_results": {
                    "similarity_analysis": result.llm_analysis.get("similarity_analysis", {}),
                    "motivation_analysis": result.llm_analysis.get("motivation_analysis", {}),
                    "analysis_summary": result.llm_analysis.get("analysis_summary", {})
                },
                "error_info": {
                    "has_error": not result.success,
                    "error_message": result.error_message
                }
            }
            
            # 使用DataService保存完整会话数据
            search_query_obj = SearchQuery(query=result.translated_query)
            google_results_obj = [GoogleSearchResult(**res) for res in result.google_results]

            search_result_obj = SearchResult(
                query=search_query_obj,
                results=google_results_obj,
                total_results=len(google_results_obj),
                search_time=result.google_search_time,
                success=result.success,
                error_message=result.error_message
            )

            # 将search_result_obj添加到complete_data中
            complete_data["search_result_obj"] = search_result_obj.to_dict()

            filepath = self.data_service.save_complete_session(
                session_id=result.session_id,
                data_to_save=complete_data
            )
            
            self.logger.info(f"完整搜索结果已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存搜索结果失败: {e}")
    
    def _build_business_flow_results(self, result: CogBridgesSearchResult) -> Dict[str, Any]:
        """构建业务流程结果"""
        return {
            "step1_google_search": {
                "success": bool(result.google_results),
                "results": result.google_results if result.google_results else [],
                "time_taken": result.google_search_time
            },
            "step2_reddit_posts": {
                "success": bool(result.reddit_posts),
                "total_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                "posts_summary": [
                    {
                        "title": post_data.get("post", {}).get("title", "")[:100],
                        "subreddit": post_data.get("post", {}).get("subreddit", ""),
                        "comments_count": len(post_data.get("comments", []))
                    }
                    for post_data in (result.reddit_posts if result.reddit_posts else [])
                ],
                "time_taken": result.reddit_posts_time
            },
            "step3_commenters_history": {
                "success": bool(result.commenters_history),
                "total_users_count": len(result.commenters_history) if result.commenters_history else 0,
                "users_analyzed": list(result.commenters_history.keys()) if result.commenters_history else [],
                "users_with_data": [
                    {
                        "username": username,
                        "subreddits": list(data.keys()) if isinstance(data, dict) else [],
                        "total_comments": sum(len(sub_data.get("comments", [])) for sub_data in data.values()) if isinstance(data, dict) else 0,
                        "total_posts": sum(len(sub_data.get("posts", [])) for sub_data in data.values()) if isinstance(data, dict) else 0
                    }
                    for username, data in (result.commenters_history.items() if result.commenters_history else [])
                    if isinstance(data, dict) and any(sub_data.get("comments") or sub_data.get("posts") for sub_data in data.values())
                ][:10],  # 限制显示前10个有数据的用户
                "time_taken": result.commenters_history_time
            },
            "step4_llm_analysis": {
                "success": bool(result.llm_analysis),
                "similarity_analysis_count": len(result.llm_analysis.get("similarity_analysis", {})) if result.llm_analysis else 0,
                "motivation_analysis_count": sum(len(motivations) for motivations in result.llm_analysis.get("motivation_analysis", {}).values()) if result.llm_analysis else 0,
                "analysis_summary": result.llm_analysis.get("analysis_summary", {}),
                "time_taken": result.llm_analysis_time
            }
        }

    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        stats = {
            "google_stats": self.google_service.get_statistics(),
            "reddit_stats": self.reddit_service.get_statistics(),
            "business_config": {
                "max_search_results": self.max_search_results,
                "max_comments_per_post": self.max_comments_per_post,
                "max_user_comments": self.max_user_comments,
                "max_user_posts": self.max_user_posts
            }
        }

        # 添加LLM服务统计（如果可用）
        if self.llm_service.configured:
            stats["llm_stats"] = self.llm_service.get_stats()

        return stats

    def _filter_valid_commenters(self, commenters: List[str]) -> List[str]:
        """
        过滤有效的评论者，移除机器人账户和无效用户

        Args:
            commenters: 原始评论者列表

        Returns:
            过滤后的评论者列表
        """
        import re

        filtered = []

        # 常见的机器人账户模式
        bot_patterns = [
            r'.*bot$',
            r'.*_bot$',
            r'bot_.*',
            r'auto.*',
            r'.*moderator.*',
            r'.*admin.*'
        ]

        # 无效用户名模式
        invalid_patterns = [
            r'^\[deleted\]$',
            r'^\[removed\]$',
            r'^deleted$',
            r'^removed$'
        ]

        for commenter in commenters:
            if not commenter or len(commenter.strip()) == 0:
                continue

            commenter_lower = commenter.lower()

            # 检查是否为无效用户
            is_invalid = any(re.match(pattern, commenter_lower) for pattern in invalid_patterns)
            if is_invalid:
                continue

            # 检查是否为机器人（可选，根据需要启用）
            # is_bot = any(re.match(pattern, commenter_lower) for pattern in bot_patterns)
            # if is_bot:
            #     continue

            # 过滤过短或过长的用户名
            if len(commenter) < 3 or len(commenter) > 20:
                continue

            filtered.append(commenter)

        return filtered

    async def _get_user_full_overview_history(self, username: str, max_items: int = 100) -> Dict[str, Any]:
        """获取单个用户的所有历史overview（帖子和评论）"""
        try:
            reddit = await self.reddit_service._ensure_async_reddit()
            redditor = await reddit.redditor(username)
            
            submissions = []
            comments = []
            total_count = 0
            start_time = time.time()
            
            # 获取所有历史overview（包括帖子和评论），按top排序
            async for item in redditor.top(limit=None):
                total_count += 1
                
                # 判断是帖子还是评论
                if hasattr(item, 'is_self') or hasattr(item, 'title'):  # 这是帖子
                    submission_info = await self._get_submission_info(item)
                    submissions.append(submission_info)
                else:  # 这是评论
                    comment_info = await self._get_comment_info(item)
                    # 只保留直接回复帖子的评论
                    if comment_info.get('is_reply_to_submission', False):
                        comments.append(comment_info)
                
                # 设置安全限制，避免无限循环
                if total_count >= max_items:
                    break
            
            end_time = time.time()
            total_time = end_time - start_time
            
            return {
                "username": username,
                "status": "success",
                "total_items": total_count,
                "submissions_count": len(submissions),
                "comments_count": len(comments),
                "total_time": total_time,
                "rate_per_second": total_count/total_time if total_time > 0 else 0,
                "submissions": submissions,
                "comments": comments,
                "reached_limit": total_count >= max_items
            }
            
        except Exception as e:
            self.logger.warning(f"获取用户 {username} overview失败: {e}")
            return {
                "username": username,
                "status": "error",
                "error": str(e),
                "total_items": 0,
                "submissions_count": 0,
                "comments_count": 0,
                "total_time": 0,
                "rate_per_second": 0,
                "submissions": [],
                "comments": [],
                "reached_limit": False
            }

    async def _get_submission_info(self, submission) -> Dict[str, Any]:
        """获取帖子的基本信息"""
        try:
            return {
                "score": getattr(submission, 'score', 0),
                "title": getattr(submission, 'title', 'N/A'),
                "selftext": getattr(submission, 'selftext', 'N/A'),
                "subreddit": getattr(submission, 'subreddit_name_prefixed', 'N/A'),
                "created_utc": getattr(submission, 'created_utc', 0)
            }
        except Exception as e:
            return {
                "error": f"获取帖子信息失败: {str(e)}",
                "score": 0,
                "title": "N/A",
                "selftext": "N/A",
                "subreddit": "N/A",
                "created_utc": 0
            }

    async def _get_comment_info(self, comment) -> Dict[str, Any]:
        """获取评论的基本信息"""
        try:
            # 判断是回复帖子还是评论
            parent_id = getattr(comment, 'parent_id', 'N/A')
            is_reply_to_submission = parent_id.startswith('t3_') if parent_id != 'N/A' else False
            
            return {
                "body": getattr(comment, 'body', 'N/A'),
                "score": getattr(comment, 'score', 0),
                "created_utc": getattr(comment, 'created_utc', 0),
                "subreddit": getattr(comment, 'subreddit_name_prefixed', 'N/A'),
                "is_reply_to_submission": is_reply_to_submission,
                "submission_title": getattr(comment, 'link_title', 'N/A')
            }
        except Exception as e:
            return {
                "error": f"获取评论信息失败: {str(e)}",
                "body": "N/A",
                "score": 0,
                "created_utc": 0,
                "subreddit": "N/A",
                "is_reply_to_submission": False,
                "submission_title": "N/A"
            }

    def _convert_overview_to_legacy_format(self, overview_result: Dict[str, Any]) -> Dict[str, Any]:
        """将新的overview格式转换为原有的legacy格式"""
        if overview_result.get('status') != 'success':
            return {}
        
        legacy_format = {}
        
        # 收集所有子版块
        all_subreddits = []
        all_subreddits.extend([s.get('subreddit', 'N/A') for s in overview_result.get('submissions', []) if s.get('subreddit') != 'N/A'])
        all_subreddits.extend([c.get('subreddit', 'N/A') for c in overview_result.get('comments', []) if c.get('subreddit') != 'N/A'])
        unique_subreddits = list(set(all_subreddits))
        
        # 处理帖子数据
        for submission in overview_result.get('submissions', []):
            subreddit = submission.get('subreddit', 'N/A')
            if subreddit not in legacy_format:
                legacy_format[subreddit] = {"posts": [], "comments": []}
            
            legacy_format[subreddit]["posts"].append({
                "id": f"post_{submission.get('created_utc', 0)}",  # 生成唯一ID
                "title": submission.get('title', 'N/A'),
                "score": submission.get('score', 0),
                "created_utc": submission.get('created_utc', 0)
            })
        
        # 处理评论数据
        for comment in overview_result.get('comments', []):
            subreddit = comment.get('subreddit', 'N/A')
            if subreddit not in legacy_format:
                legacy_format[subreddit] = {"posts": [], "comments": []}
            
            legacy_format[subreddit]["comments"].append({
                "id": f"comment_{comment.get('created_utc', 0)}",  # 生成唯一ID
                "body": comment.get('body', 'N/A')[:200] + "..." if len(comment.get('body', '')) > 200 else comment.get('body', 'N/A'),
                "score": comment.get('score', 0),
                "created_utc": comment.get('created_utc', 0)
            })
        
        # 添加子版块列表信息
        legacy_format["_metadata"] = {
            "subreddits": unique_subreddits,
            "unique_subreddits_count": len(unique_subreddits),
            "total_posts": len(overview_result.get('submissions', [])),
            "total_comments": len(overview_result.get('comments', [])),
            "username": overview_result.get('username', ''),
            "total_items": overview_result.get('total_items', 0),
            "rate_per_second": overview_result.get('rate_per_second', 0)
        }
        
        return legacy_format

    async def _step4_llm_analysis(self, result: CogBridgesSearchResult) -> Dict[str, Any]:
        """步骤4: 执行LLM分析功能"""
        llm_results = {
            "similarity_analysis": {},
            "motivation_analysis": {},
            "analysis_summary": {},
            "success": False,
            "error": None
        }
        
        try:
            # 检查LLM服务配置
            if not self.llm_service.configured:
                llm_results["error"] = "LLM服务未配置"
                return llm_results
            
            # 提取用户和subreddit信息
            users_data = self._extract_users_and_subreddits(result)
            self.logger.info(f"提取到 {len(users_data)} 个用户数据")
            
            if not users_data:
                llm_results["error"] = "未找到有效的用户数据"
                return llm_results
            
            # 执行相似subreddit筛选
            self.logger.info("执行相似subreddit筛选...")
            similarity_results = await self._analyze_subreddit_similarity(users_data, result)
            llm_results["similarity_analysis"] = similarity_results
            
            # 执行用户评论动机分析
            self.logger.info("执行用户评论动机分析...")
            motivation_results = await self._analyze_comment_motivation(users_data, result, similarity_results)
            llm_results["motivation_analysis"] = motivation_results
            
            # 生成分析总结
            llm_results["analysis_summary"] = self._generate_analysis_summary(similarity_results, motivation_results)
            llm_results["success"] = True
            
            return llm_results
            
        except Exception as e:
            self.logger.error(f"LLM分析失败: {e}")
            llm_results["error"] = str(e)
            return llm_results
    
    def _extract_users_and_subreddits(self, search_result: CogBridgesSearchResult) -> List[Dict[str, Any]]:
        """从搜索结果中提取用户和subreddit信息"""
        users_data = []
        
        if not search_result.commenters_history:
            return users_data
        
        for username, user_data in search_result.commenters_history.items():
            if not isinstance(user_data, dict):
                continue
                
            # 获取用户的subreddits列表
            user_subreddits = []
            if '_metadata' in user_data:
                metadata = user_data['_metadata']
                user_subreddits = metadata.get('subreddits', [])
            
            # 如果没有metadata，从用户数据中提取subreddits
            if not user_subreddits:
                user_subreddits = [sr for sr in user_data.keys() if sr != '_metadata']
            
            if user_subreddits:
                users_data.append({
                    "username": username,
                    "user_subreddits": user_subreddits,
                    "user_data": user_data
                })
        
        return users_data
    
    async def _analyze_subreddit_similarity(self, users_data: List[Dict[str, Any]], search_result: CogBridgesSearchResult) -> Dict[str, Any]:
        """分析subreddit相似性"""
        similarity_results = {}
        
        # 从Reddit帖子中提取目标subreddits
        target_subreddits = self._extract_target_subreddits(search_result)
        target_subreddit_list = list(target_subreddits)
        self.logger.info(f"目标subreddits: {target_subreddit_list}")
        
        # 使用批量分析，每个用户只调用一次LLM
        tasks = []
        for user_info in users_data:
            tasks.append(self._analyze_single_user_similarity_batch(
                user_info["username"],
                user_info["user_subreddits"],
                target_subreddit_list
            ))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            similarity_results = self._process_similarity_results(results)
        
        return similarity_results
    
    def _extract_target_subreddits(self, search_result: CogBridgesSearchResult) -> set:
        """提取目标subreddits"""
        target_subreddits = set()
        if search_result.reddit_posts:
            for post_data in search_result.reddit_posts:
                if 'post' in post_data:
                    subreddit = post_data['post'].get('subreddit')
                    if subreddit:
                        target_subreddits.add(subreddit)
        
        # 如果没有找到目标subreddits，使用默认的AI相关subreddits
        if not target_subreddits:
            target_subreddits = {'ClaudeAI', 'ChatGPT', 'OpenAI', 'artificial'}
        
        return target_subreddits
    
    def _process_similarity_results(self, results: List[Any]) -> Dict[str, Any]:
        """处理相似性分析结果"""
        similarity_results = {}
        
        for result in results:
            if isinstance(result, Exception):
                continue
                
            username = result.get("username")
            batch_results = result.get("batch_results", {})
            original_subreddits = result.get("original_subreddits", [])
            
            if username not in similarity_results:
                similarity_results[username] = {
                    "original_subreddits": original_subreddits,
                    "target_analysis": {},
                    "all_similar_subreddits": set()
                }
            
            # 将批量结果转换为优化后的格式
            for target_subreddit, similar_subreddits in batch_results.items():
                similarity_results[username]["target_analysis"][target_subreddit] = {
                    "similar_subreddits": similar_subreddits,
                    "similarity_count": len(similar_subreddits)
                }
                # 收集所有相似subreddits
                similarity_results[username]["all_similar_subreddits"].update(similar_subreddits)
        
        # 将set转换为list以便JSON序列化
        for username in similarity_results:
            similarity_results[username]["all_similar_subreddits"] = list(similarity_results[username]["all_similar_subreddits"])
            similarity_results[username]["total_similar_count"] = len(similarity_results[username]["all_similar_subreddits"])
        
        return similarity_results
    
    async def _analyze_single_user_similarity_batch(self, username: str, user_subreddits: List[str], target_subreddits: List[str]) -> Dict[str, Any]:
        """批量分析单个用户的subreddit相似性"""
        try:
            batch_results = await self.llm_service.filter_similar_subreddits_batch(
                user_subreddits=user_subreddits,
                target_subreddits=target_subreddits,
                user_id=username
            )
            
            return {
                "username": username,
                "original_subreddits": user_subreddits,
                "batch_results": batch_results
            }
            
        except Exception as e:
            self.logger.error(f"用户{username}的批量相似性分析失败: {e}")
            return {
                "username": username,
                "error": str(e)
            }
    
    async def _analyze_comment_motivation(self, users_data: List[Dict[str, Any]], search_result: CogBridgesSearchResult, similarity_results: Dict[str, Any]) -> Dict[str, Any]:
        """分析用户评论动机"""
        motivation_results = {}
        data_statistics = {}
        
        # 构建用户评论分析任务
        tasks = []
        
        for user_info in users_data:
            username = user_info["username"]
            user_data = user_info["user_data"]
            
            # 查找用户的评论和对应的帖子
            user_comments = self._find_user_comments_in_posts(username, search_result)
            
            # 记录原始数据统计
            original_posts_count = len(set(comment["post"]["id"] for comment in user_comments))
            original_comments_count = len(user_comments)
            
            # 根据相似性分析结果筛选评论
            filtered_comments = self._filter_comments_by_similarity(
                user_comments, username, similarity_results
            )
            
            # 记录筛选后数据统计
            filtered_posts_count = len(set(comment["post"]["id"] for comment in filtered_comments))
            filtered_comments_count = len(filtered_comments)
            
            # 保存统计信息
            data_statistics[username] = {
                "original_posts_count": original_posts_count,
                "original_comments_count": original_comments_count,
                "filtered_posts_count": filtered_posts_count,
                "filtered_comments_count": filtered_comments_count,
                "filter_ratio": {
                    "posts": filtered_posts_count / original_posts_count if original_posts_count > 0 else 0,
                    "comments": filtered_comments_count / original_comments_count if original_comments_count > 0 else 0
                }
            }
            
            for comment_info in filtered_comments:
                tasks.append(self._analyze_single_comment_motivation(
                    username, comment_info, user_info["user_subreddits"], user_data
                ))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            motivation_results = self._process_motivation_results(results)
        
        # 将统计信息添加到结果中
        motivation_results["data_statistics"] = data_statistics
        
        return motivation_results
    
    def _process_motivation_results(self, results: List[Any]) -> Dict[str, Any]:
        """处理动机分析结果"""
        motivation_results = {}
        
        for result in results:
            if isinstance(result, Exception):
                continue
                
            username = result.get("user_id")
            if username not in motivation_results:
                motivation_results[username] = []
            
            motivation_results[username].append(result)
        
        return motivation_results
    
    def _find_user_comments_in_posts(self, username: str, search_result: CogBridgesSearchResult) -> List[Dict[str, Any]]:
        """在帖子中查找用户的评论"""
        user_comments = []
        
        if not search_result.reddit_posts:
            return user_comments
        
        for post_data in search_result.reddit_posts:
            if 'post' not in post_data or 'comments' not in post_data:
                continue
                
            post = post_data['post']
            comments = post_data['comments']
            
            # 查找该用户在这个帖子中的评论
            for comment in comments:
                if comment.get('author') == username:
                    user_comments.append({
                        "post": post,
                        "comment": comment,
                        "subreddit": post.get('subreddit')
                    })
        
        return user_comments
    
    def _filter_comments_by_similarity(self, user_comments: List[Dict[str, Any]], username: str, similarity_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据相似性分析结果筛选评论"""
        if username not in similarity_results:
            return user_comments
        
        user_similarity = similarity_results[username]
        similar_subreddits = user_similarity.get("all_similar_subreddits", [])
        
        if not similar_subreddits:
            return user_comments
        
        # 筛选出在相似subreddits中的评论
        filtered_comments = []
        for comment_info in user_comments:
            comment_subreddit = comment_info["subreddit"]
            # 检查评论的subreddit是否在相似subreddits列表中
            if any(similar_sub in comment_subreddit or comment_subreddit in similar_sub 
                   for similar_sub in similar_subreddits):
                filtered_comments.append(comment_info)
        
        return filtered_comments
    
    async def _analyze_single_comment_motivation(self, username: str, comment_info: Dict[str, Any], user_subreddits: List[str], user_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析单个评论的动机"""
        try:
            # 构建相似subreddits数据
            similar_subreddits_data = self._build_similar_subreddits_data(user_subreddits, user_data)
            
            # 构建用户概览
            user_overview = {
                "subreddits": user_subreddits,
                "user_type": "unknown",
                "activity_level": "medium"
            }
            
            # 调用LLM分析
            result = await self.llm_service.analyze_user_comment_motivation(
                user_id=username,
                target_subreddit=comment_info["subreddit"],
                target_post=comment_info["post"],
                user_comment=comment_info["comment"],
                similar_subreddits_data=similar_subreddits_data,
                user_overview=user_overview
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"用户{username}评论动机分析失败: {e}")
            return {
                "user_id": username,
                "error": str(e)
            }
    
    def _build_similar_subreddits_data(self, user_subreddits: List[str], user_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建相似subreddits数据"""
        similar_subreddits_data = []
        for subreddit in user_subreddits:
            if subreddit in user_data:
                sub_data = user_data[subreddit]
                similar_subreddits_data.append({
                    "subreddit": subreddit,
                    "posts": sub_data.get("posts", []),
                    "comments": sub_data.get("comments", []),
                    "user_engagement": "high" if len(sub_data.get("comments", [])) > 5 else "medium"
                })
        return similar_subreddits_data
    
    def _generate_analysis_summary(self, similarity_results: Dict[str, Any], motivation_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析总结"""
        # 处理动机分析结果，排除data_statistics
        data_statistics = motivation_results.pop("data_statistics", {})
        user_motivations = {k: v for k, v in motivation_results.items() if k != "data_statistics"}
        
        summary = {
            "total_users_analyzed": len(similarity_results),
            "total_motivations_analyzed": sum(len(motivations) for motivations in user_motivations.values()),
            "top_similar_subreddits": {},
            "user_type_distribution": {},
            "data_filtering_statistics": data_statistics,
            "key_insights": []
        }
        
        # 统计最常见的相似subreddits
        summary["top_similar_subreddits"] = self._calculate_top_similar_subreddits(similarity_results)
        
        # 分析用户类型分布
        summary["user_type_distribution"] = self._calculate_user_type_distribution(user_motivations)
        
        # 生成关键洞察
        summary["key_insights"] = self._generate_key_insights(summary)
        
        # 恢复data_statistics到motivation_results
        motivation_results["data_statistics"] = data_statistics
        
        return summary
    
    def _calculate_top_similar_subreddits(self, similarity_results: Dict[str, Any]) -> Dict[str, int]:
        """计算最常见的相似subreddits"""
        subreddit_counts = {}
        for user_results in similarity_results.values():
            target_analysis = user_results.get("target_analysis", {})
            for target_results in target_analysis.values():
                for subreddit in target_results.get("similar_subreddits", []):
                    subreddit_counts[subreddit] = subreddit_counts.get(subreddit, 0) + 1
        
        return dict(sorted(subreddit_counts.items(), key=lambda x: x[1], reverse=True)[:10])
    
    def _calculate_user_type_distribution(self, motivation_results: Dict[str, Any]) -> Dict[str, int]:
        """计算用户类型分布"""
        user_types = {}
        for user_motivations in motivation_results.values():
            for motivation in user_motivations:
                user_profile = motivation.get("user_profile", "")
                if "开发" in user_profile or "程序" in user_profile:
                    user_types["developer"] = user_types.get("developer", 0) + 1
                elif "初学" in user_profile or "学习" in user_profile:
                    user_types["learner"] = user_types.get("learner", 0) + 1
                elif "研究" in user_profile or "科学" in user_profile:
                    user_types["researcher"] = user_types.get("researcher", 0) + 1
                else:
                    user_types["other"] = user_types.get("other", 0) + 1
        
        return user_types
    
    def _generate_key_insights(self, summary: Dict[str, Any]) -> List[str]:
        """生成关键洞察"""
        insights = []
        
        if summary["total_users_analyzed"] > 0:
            insights.append(f"分析了{summary['total_users_analyzed']}个用户的subreddit相似性")
        
        if summary["total_motivations_analyzed"] > 0:
            insights.append(f"深度分析了{summary['total_motivations_analyzed']}条评论的动机")
        
        if summary["top_similar_subreddits"]:
            top_subreddit = list(summary["top_similar_subreddits"].keys())[0]
            insights.append(f"最常见的相似subreddit是{top_subreddit}")
        
        # 添加数据筛选统计洞察
        data_stats = summary.get("data_filtering_statistics", {})
        if data_stats:
            total_original_posts = sum(stats.get("original_posts_count", 0) for stats in data_stats.values())
            total_filtered_posts = sum(stats.get("filtered_posts_count", 0) for stats in data_stats.values())
            total_original_comments = sum(stats.get("original_comments_count", 0) for stats in data_stats.values())
            total_filtered_comments = sum(stats.get("filtered_comments_count", 0) for stats in data_stats.values())
            
            if total_original_posts > 0:
                post_filter_ratio = total_filtered_posts / total_original_posts
                insights.append(f"相似性筛选将帖子数量从{total_original_posts}减少到{total_filtered_posts} ({post_filter_ratio:.1%})")
            
            if total_original_comments > 0:
                comment_filter_ratio = total_filtered_comments / total_original_comments
                insights.append(f"相似性筛选将评论数量从{total_original_comments}减少到{total_filtered_comments} ({comment_filter_ratio:.1%})")
        
        return insights
