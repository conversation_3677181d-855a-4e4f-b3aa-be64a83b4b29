// 导入React的Hooks：useState用于状态管理，useEffect用于副作用处理
import { useState, useEffect } from 'react'
// 导入React Router的导航Hook，用于页面跳转
import { useNavigate } from 'react-router-dom'
// 导入自定义组件
import SearchInput from '../components/SearchInput'        // 搜索输入框组件
import StyleDiagnostic from '../components/StyleDiagnostic' // 风格诊断组件
// 导入Lucide React图标库的图标组件
import { History, Sparkles, AlertCircle } from 'lucide-react'
// 导入API服务模块
import { api } from '../services/api'

// 首页组件 - 这是应用的主入口页面
const HomePage = () => {
  // 获取导航函数，用于页面跳转
  const navigate = useNavigate()
  
  // 状态管理：使用useState Hook
  // isLoading: 控制加载状态
  const [isLoading, setIsLoading] = useState(false)
  // apiStatus: 控制API状态显示，可能的值：'checking', 'healthy', 'error'
  const [apiStatus, setApiStatus] = useState('checking')

  // 示例问题模板 - 预设的常见问题，用户可以点击快速开始
  const exampleQuestions = [
    "我该不该裸辞？",
    "30岁转行程序员来得及吗？",
    "如何选择适合的工作？",
    "副业推荐有哪些？",
    "远程工作的利弊分析",
    "投资理财入门指导"
  ]

  // 检查后端API状态 - 使用useEffect Hook在组件挂载时执行
  useEffect(() => {
    // 定义异步函数来检查API健康状态
    const checkApiHealth = async () => {
      try {
        // 调用API健康检查接口
        await api.healthCheck()
        // 如果成功，设置状态为健康
        setApiStatus('healthy')
      } catch (error) {
        // 如果失败，打印错误并设置状态为错误
        console.error('API健康检查失败:', error)
        setApiStatus('error')
      }
    }
    
    // 立即执行健康检查
    checkApiHealth()
  }, []) // 空依赖数组表示只在组件挂载时执行一次

  // 处理搜索请求的函数
  const handleSearch = async (query) => {
    // 设置加载状态为true
    setIsLoading(true)

    // 立即跳转到加载页面，传递查询参数
    // navigate函数的第二个参数是state，可以传递数据到目标页面
    navigate('/loading', {
      state: {
        query,                    // 搜索查询内容
        sessionId: Date.now().toString(), // 生成唯一的会话ID
        timestamp: Date.now()     // 记录时间戳
      }
    })
  }

  // 处理示例问题点击的函数
  const handleExampleClick = (question) => {
    // 直接调用搜索函数，传入点击的问题
    handleSearch(question)
  }

  // 跳转到历史记录页面的函数
  const goToHistory = () => {
    navigate('/history')
  }

  // 根据API状态返回对应的状态指示器组件
  const getStatusIndicator = () => {
    switch (apiStatus) {
      case 'checking':
        return <span className="text-yellow-600">检查中...</span>
      case 'healthy':
        return <span className="text-green-600">✅ 服务正常</span>
      case 'error':
        return <span className="text-red-600">❌ 服务异常</span>
      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 头部导航 */}
      <header className="flex justify-between items-center p-6">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">C</span>
          </div>
          <div>
            <span className="text-gray-600 text-sm">CogBridges v2.0</span>
            <div className="text-xs">{getStatusIndicator()}</div>
          </div>
        </div>
        
        <button
          onClick={goToHistory}
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
        >
          <History className="w-4 h-4" />
          <span className="text-sm">历史记录</span>
        </button>
      </header>

      {/* API状态警告 */}
      {apiStatus === 'error' && (
        <div className="mx-6 mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-3">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <div>
            <p className="text-red-800 font-medium">后端服务连接失败</p>
            <p className="text-red-600 text-sm">请确保后端服务器正在运行 (http://localhost:5000)</p>
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <main className="flex-1 flex flex-col items-center justify-center px-6 pb-20">
        
        {/* Logo和标语 */}
        <div className="text-center mb-12 animate-fade-in">
          <h1 className="text-5xl font-light text-gray-800 mb-4 tracking-tight">
            Cog<span className="text-primary-500 font-medium">Bridges</span>
          </h1>
          <p className="text-xl text-gray-600 mb-2">连接认知，桥接智慧</p>
          <p className="text-gray-500">为你找到最懂你的回答</p>
        </div>

        {/* 搜索框 */}
        <div className="w-full max-w-2xl mb-8 animate-slide-up">
          <SearchInput 
            onSearch={handleSearch}
            placeholder="比如：我该不该裸辞？"
            className="w-full"
          />
        </div>

        {/* 搜索范围说明 */}
        <div className="mb-8 text-center">
          <p className="text-sm text-gray-500 mb-2">
            基于 <span className="text-primary-600 font-medium">Reddit</span> 深度分析用户背景
          </p>
          <div className="flex items-center justify-center space-x-4 text-xs text-gray-400">
            <span>🧠 智能动机建模</span>
            <span>•</span>
            <span>👥 真实用户洞察</span>
            <span>•</span>
            <span>🎯 精准答案筛选</span>
          </div>
        </div>

        {/* 示例问题 */}
        <div className="w-full max-w-4xl">
          <h3 className="text-center text-gray-700 font-medium mb-6">或者试试这些热门问题</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {exampleQuestions.map((question, index) => (
              <button
                key={index}
                onClick={() => handleExampleClick(question)}
                disabled={isLoading || apiStatus !== 'healthy'}
                className="group p-4 bg-white border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-md transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <div className="flex items-start space-x-3">
                  <Sparkles className="w-5 h-5 text-primary-400 mt-0.5 group-hover:text-primary-500" />
                  <span className="text-gray-700 group-hover:text-gray-800 leading-relaxed">
                    {question}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 特色介绍 */}
        <div className="mt-16 text-center max-w-3xl">
          <h3 className="text-lg font-medium text-gray-800 mb-4">为什么选择 CogBridges？</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
            <div className="space-y-2">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                🎯
              </div>
              <h4 className="font-medium text-gray-700">精准匹配</h4>
              <p className="text-gray-500">基于用户历史发言分析，找到真正理解你的人</p>
            </div>
            <div className="space-y-2">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                🧠
              </div>
              <h4 className="font-medium text-gray-700">深度洞察</h4>
              <p className="text-gray-500">AI分析用户动机和背景，提供可信度评估</p>
            </div>
            <div className="space-y-2">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                ⚡
              </div>
              <h4 className="font-medium text-gray-700">快速获取</h4>
              <p className="text-gray-500">一键搜索，获得高质量的个性化建议</p>
            </div>
          </div>
        </div>
      </main>

      {/* 页脚 */}
      <footer className="text-center py-6 border-t border-gray-200 bg-white">
        <p className="text-gray-500 text-sm">
          © 2024 CogBridges. 连接智慧，理解你的选择。
        </p>
      </footer>

      {/* 样式诊断组件 */}
      <StyleDiagnostic />
    </div>
  )
}

export default HomePage 