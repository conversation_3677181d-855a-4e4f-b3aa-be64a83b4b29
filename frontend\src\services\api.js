// 导入axios HTTP客户端库，用于发送API请求
import axios from 'axios'

// 从环境变量获取API基础URL，默认使用localhost:5000
// import.meta.env是Vite提供的环境变量访问方式
// VITE_API_URL是自定义的环境变量，必须以VITE_开头
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000'

// 创建axios实例 - 这是一个预配置的HTTP客户端
// 可以设置默认的baseURL、超时时间、请求头等
const apiClient = axios.create({
  baseURL: API_BASE_URL,        // 所有请求的基础URL
  timeout: 120000,              // 120秒超时（2分钟），因为LLM分析可能需要较长时间
  headers: {
    'Content-Type': 'application/json',  // 设置默认请求头，告诉服务器发送的是JSON数据
  }
})

// 请求拦截器 - 在发送请求前执行的函数
// 可以用于添加认证信息、日志记录、请求预处理等
apiClient.interceptors.request.use(
  (config) => {
    // 记录请求日志，方便调试
    console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`)
    return config  // 必须返回config，否则请求不会发送
  },
  (error) => {
    // 请求配置错误时的处理
    console.error('❌ API请求错误:', error)
    return Promise.reject(error)  // 拒绝Promise，让错误继续传播
  }
)

// 响应拦截器 - 在收到响应后执行的函数
// 可以用于统一处理响应数据、错误处理、日志记录等
apiClient.interceptors.response.use(
  (response) => {
    // 记录成功响应日志
    console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`)
    return response  // 返回响应数据
  },
  (error) => {
    // 响应错误处理（如网络错误、服务器错误等）
    console.error('❌ API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)  // 拒绝Promise，让错误继续传播
  }
)

// API方法集合 - 封装所有与后端通信的方法
export const api = {
  // 健康检查 - 检查后端服务是否正常运行
  async healthCheck() {
    try {
      // 发送GET请求到/api/health端点
      const response = await apiClient.get('/api/health')
      return response.data  // 返回响应数据
    } catch (error) {
      // 如果请求失败，抛出带有中文描述的错误
      throw new Error(`健康检查失败: ${error.message}`)
    }
  },

  // 搜索接口 - 主要的搜索功能（异步版本）
  async search(query) {
    try {
      // 发送POST请求到/api/search端点，传递搜索参数
      const response = await apiClient.post('/api/search', { 
        query,                    // 搜索查询内容
        enhanced: true,           // 启用增强功能
        llm_analysis: true        // 启用LLM分析
      })
      return response.data
    } catch (error) {
      throw new Error(`搜索失败: ${error.message}`)
    }
  },

  // 获取搜索进度
  async getSearchProgress(sessionId) {
    try {
      // 发送GET请求获取指定会话的搜索进度
      const response = await apiClient.get(`/api/search/progress/${sessionId}`)
      return response.data
    } catch (error) {
      throw new Error(`获取搜索进度失败: ${error.message}`)
    }
  },

  // 轮询搜索进度直到完成
  async pollSearchProgress(sessionId, onProgress = null, maxWaitTime = 300000) { // 5分钟超时
    const startTime = Date.now()
    const pollInterval = 2000 // 2秒轮询一次

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const progressData = await this.getSearchProgress(sessionId)
        
        if (progressData.success) {
          // 调用进度回调
          if (onProgress) {
            onProgress(progressData)
          }

          // 检查是否完成
          if (progressData.status === 'completed') {
            return progressData.result
          } else if (progressData.status === 'error') {
            throw new Error(progressData.error || '搜索失败')
          }
        } else {
          throw new Error(progressData.error || '获取进度失败')
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        console.error('轮询搜索进度失败:', error)
        throw error
      }
    }

    throw new Error('搜索超时')
  },

  // 获取搜索结果详情
  async getResultDetail(sessionId) {
    try {
      // 发送GET请求获取指定会话的详细信息
      const response = await apiClient.get(`/api/sessions/${sessionId}`)
      return response.data
    } catch (error) {
      throw new Error(`获取详情失败: ${error.message}`)
    }
  },

  // 获取搜索历史
  async getSearchHistory() {
    try {
      // 发送GET请求获取用户的搜索历史记录
      const response = await apiClient.get('/api/history')
      return response.data
    } catch (error) {
      // 记录错误但不抛出，因为历史记录不是核心功能
      console.error('获取搜索历史失败:', error)
      throw new Error(`获取搜索历史失败: ${error.message}`)
    }
  },

  // 添加收藏
  async addBookmark(resultData) {
    try {
      // 发送POST请求添加收藏
      const response = await apiClient.post('/api/bookmarks', resultData)
      return response.data
    } catch (error) {
      throw new Error(`添加收藏失败: ${error.message}`)
    }
  },

  // 获取收藏列表
  async getBookmarks() {
    try {
      const response = await apiClient.get('/api/bookmarks')
      return response.data
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      throw new Error(`获取收藏列表失败: ${error.message}`)
    }
  },

  // 删除收藏
  async removeBookmark(bookmarkId) {
    try {
      const response = await apiClient.delete(`/api/bookmarks/${bookmarkId}`)
      return response.data
    } catch (error) {
      throw new Error(`删除收藏失败: ${error.message}`)
    }
  }
}

// 导出默认实例
export default api 