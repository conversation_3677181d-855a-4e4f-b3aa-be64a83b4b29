// 导入React Router的路由组件
// BrowserRouter: 使用HTML5 History API的路由器
// Routes: 路由容器，包含所有路由规则
// Route: 单个路由规则定义
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
// 导入各个页面组件
import HomePage from './pages/HomePage'        // 首页
import LoadingPage from './pages/LoadingPage'  // 加载页面
import ResultsPage from './pages/ResultsPage'  // 结果页面
import DetailPage from './pages/DetailPage'    // 详情页面
import HistoryPage from './pages/HistoryPage'  // 历史页面
import TestPage from './pages/TestPage'        // 测试页面

// 主应用组件 - 这是整个应用的根组件
// 职责：配置路由规则，不负责样式设置
// 设计原则：每个页面组件应该自己管理自己的样式，保持组件的独立性
function App() {
  return (
    // Router组件包裹整个应用，提供路由功能
    <Router>
      {/* 应用容器 - 不设置具体样式，让各个页面组件自己处理样式 */}
      {/* 这样做的好处：
          1. 保持路由配置的纯粹性，只负责路由逻辑
          2. 每个页面可以有不同的样式需求
          3. 提高组件的可复用性和维护性 */}
      <div>
        {/* Routes组件包含所有路由规则 */}
        <Routes>
          {/* 首页路由 - 路径为根路径"/" */}
          <Route path="/" element={<HomePage />} />
          
          {/* 加载页面路由 - 路径为"/loading" */}
          <Route path="/loading" element={<LoadingPage />} />
          
          {/* 结果页面路由 - 路径为"/results" */}
          <Route path="/results" element={<ResultsPage />} />
          
          {/* 详情页面路由 - 路径为"/detail/:id"，:id是动态参数 */}
          {/* 例如：/detail/123 会匹配这个路由，id值为"123" */}
          <Route path="/detail/:id" element={<DetailPage />} />
          
          {/* 历史页面路由 - 路径为"/history" */}
          <Route path="/history" element={<HistoryPage />} />
          
          {/* 测试页面路由 - 路径为"/test" */}
          <Route path="/test" element={<TestPage />} />
        </Routes>
      </div>
    </Router>
  )
}

// 导出App组件，供其他文件使用
export default App
