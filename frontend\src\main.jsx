// 导入React的严格模式组件，用于开发时检测潜在问题
import { StrictMode } from 'react'
// 导入React 18的新API，用于创建根节点
import { createRoot } from 'react-dom/client'
// 导入全局样式文件
import './index.css'
// 导入主应用组件
import App from './App.jsx'

// 创建React应用的根节点并渲染应用
// document.getElementById('root') 获取HTML中的挂载点
// createRoot() 创建React 18的根节点
// render() 方法将React组件渲染到DOM中
createRoot(document.getElementById('root')).render(
  // StrictMode是React的严格模式，只在开发环境生效
  // 它会：
  // 1. 检测不安全的生命周期方法
  // 2. 检测意外的副作用
  // 3. 检测过时的API使用
  // 4. 确保可重用状态
  <StrictMode>
    {/* 渲染主应用组件 */}
    <App />
  </StrictMode>,
)
