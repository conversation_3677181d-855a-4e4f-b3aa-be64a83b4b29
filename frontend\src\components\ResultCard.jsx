import { useState } from 'react'
import { User, MessageCircle, Heart, ExternalLink, Eye, Bookmark } from 'lucide-react'

const ResultCard = ({ result, onViewDetail, onBookmark }) => {
  const [isBookmarked, setIsBookmarked] = useState(false)

  const handleBookmark = () => {
    setIsBookmarked(!isBookmarked)
    onBookmark?.(result, !isBookmarked)
  }

  const getPersonaTags = (insights) => {
    // 根据洞察内容生成标签
    const tags = []
    if (insights?.includes('经验丰富')) tags.push('亲身经历')
    if (insights?.includes('理性分析')) tags.push('理性思考')
    if (insights?.includes('情感支持')) tags.push('温暖陪伴')
    if (insights?.includes('专业')) tags.push('专业见解')
    return tags.length > 0 ? tags : ['深度思考']
  }

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-200">
      {/* 回答摘要 */}
      <div className="mb-4">
        <div className="text-lg font-medium text-gray-800 mb-2 leading-relaxed">
          <div className="whitespace-pre-wrap">{result.fullComment || result.comment}</div>
        </div>
        <div className="flex items-center text-sm text-gray-500 space-x-4">
          <span className="flex items-center">
            <MessageCircle className="w-4 h-4 mr-1" />
            来自 r/{result.subreddit}
          </span>
          <span className="flex items-center">
            <Heart className="w-4 h-4 mr-1" />
            {result.score} 赞
          </span>
        </div>
      </div>

      {/* 作者信息 */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <User className="w-5 h-5 text-gray-400 mr-2" />
            <span className="font-medium text-gray-700">
              u/{result.author}
            </span>
          </div>

        </div>
        
        {/* 背景洞察 */}
        <div className="text-sm text-gray-600">
          <p className="mb-2">
            <strong>背景洞察:</strong> {result.insights || "该用户在相关领域有丰富经验，发言理性客观"}
          </p>
        </div>
      </div>

      {/* 观点标签 */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-2">
          {getPersonaTags(result.insights).map((tag, index) => (
            <span
              key={index}
              className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium"
            >
              #{tag}
            </span>
          ))}
        </div>
      </div>

      {/* 推荐理由 */}
      <div className="mb-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
        <p className="text-sm text-blue-800">
          <strong>为什么推荐:</strong> {result.recommendation || "该回答基于真实经历，观点平衡理性，能提供有价值的参考"}
        </p>
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        <button
          onClick={() => onViewDetail?.(result)}
          className="flex items-center px-4 py-2 text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded-lg transition-colors"
        >
          <Eye className="w-4 h-4 mr-2" />
          查看详情
        </button>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleBookmark}
            className={`p-2 rounded-lg transition-colors ${
              isBookmarked 
                ? 'text-yellow-600 bg-yellow-50 hover:bg-yellow-100' 
                : 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50'
            }`}
            title={isBookmarked ? '取消收藏' : '收藏'}
          >
            <Bookmark className={`w-4 h-4 ${isBookmarked ? 'fill-current' : ''}`} />
          </button>

          <a
            href={result.url}
            target="_blank"
            rel="noopener noreferrer"
            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="查看原帖"
          >
            <ExternalLink className="w-4 h-4" />
          </a>
        </div>
      </div>
    </div>
  )
}

export default ResultCard 