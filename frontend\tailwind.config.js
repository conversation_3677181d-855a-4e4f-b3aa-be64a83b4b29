/** @type {import('tailwindcss').Config} */
// Tailwind CSS配置文件
// Tailwind是一个原子化CSS框架，通过预定义的类名快速构建样式
export default {
  // 指定需要扫描的文件，Tailwind会分析这些文件中的类名使用情况
  // 只打包实际使用到的CSS类，减少最终文件大小
  content: [
    "./index.html",  // 主HTML文件
    "./src/**/*.{js,ts,jsx,tsx}",  // src目录下所有的JavaScript/TypeScript文件
  ],
  // 主题配置：自定义颜色、字体、动画等
  theme: {
    // 扩展默认主题，而不是完全覆盖
    extend: {
      // 字体配置
      fontFamily: {
        // 设置默认无衬线字体，优先使用Inter字体
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      // 自定义颜色配置
      colors: {
        // 主色调配置，定义不同深浅的蓝色
        primary: {
          50: '#f0f9ff',   // 最浅的蓝色，用于背景
          100: '#e0f2fe',  // 浅蓝色
          500: '#0ea5e9',  // 标准蓝色，主要按钮等
          600: '#0284c7',  // 深蓝色，悬停状态
          700: '#0369a1',  // 最深的蓝色，激活状态
        },
      },
      // 自定义动画配置
      animation: {
        // 淡入动画，0.5秒完成
        'fade-in': 'fadeIn 0.5s ease-in-out',
        // 向上滑动动画，0.3秒完成
        'slide-up': 'slideUp 0.3s ease-out',
        // 慢速脉冲动画，2秒循环一次
        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      // 关键帧动画定义
      keyframes: {
        // 淡入动画的关键帧
        fadeIn: {
          '0%': { opacity: '0' },    // 开始时完全透明
          '100%': { opacity: '1' },  // 结束时完全不透明
        },
        // 向上滑动动画的关键帧
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },  // 开始时向下偏移20px且透明
          '100%': { transform: 'translateY(0)', opacity: '1' },   // 结束时回到原位且不透明
        },
      }
    },
  },
  // 插件配置：可以添加Tailwind插件来扩展功能
  plugins: [],
}

