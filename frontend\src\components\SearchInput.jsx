// 导入React Hooks：useState用于状态管理，useRef用于DOM引用，useEffect用于副作用
import { useState, useRef, useEffect } from 'react'
// 导入Lucide React图标：Search搜索图标，X关闭图标
import { Search, X } from 'lucide-react'

// 搜索输入框组件 - 一个功能完整的搜索组件，支持建议、键盘导航等
// props参数说明：
// onSearch: 搜索回调函数，当用户提交搜索时调用
// placeholder: 输入框占位符文本，默认为"我该不该裸辞？"
// className: 额外的CSS类名，用于自定义样式
const SearchInput = ({ onSearch, placeholder = "我该不该裸辞？", className = "" }) => {
  // 状态管理
  const [query, setQuery] = useState('')           // 当前输入的搜索查询
  const [showSuggestions, setShowSuggestions] = useState(false)  // 是否显示搜索建议
  const [selectedIndex, setSelectedIndex] = useState(-1)         // 当前选中的建议索引
  const inputRef = useRef(null)                    // 输入框的DOM引用，用于聚焦等操作

  // 预定义的搜索建议 - 当用户输入时，会从这些建议中筛选匹配的内容
  const suggestions = [
    "我该不该裸辞？",
    "Python学习路线建议？",
    "如何选择适合的工作？", 
    "副业推荐有哪些？",
    "30岁转行程序员来得及吗？",
    "远程工作的利弊分析",
    "投资理财入门指导",
    "如何提高工作效率？",
    "创业需要准备什么？",
    "学历重要还是能力重要？"
  ]

  // 根据用户输入筛选建议列表
  // 1. 将建议和查询都转为小写进行比较（不区分大小写）
  // 2. 只显示包含查询内容的建议
  // 3. 只在查询长度大于0时显示建议
  // 4. 限制最多显示5个建议
  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(query.toLowerCase()) && query.length > 0
  ).slice(0, 5)

  // 处理表单提交事件
  const handleSubmit = (e) => {
    e.preventDefault()  // 阻止表单默认提交行为
    if (query.trim()) {  // 确保查询不为空（去除首尾空格）
      onSearch(query.trim())  // 调用父组件传入的搜索回调函数
      setShowSuggestions(false)  // 隐藏建议列表
    }
  }

  // 处理输入框内容变化
  const handleInputChange = (e) => {
    setQuery(e.target.value)  // 更新查询内容
    setSelectedIndex(-1)      // 重置选中索引
    setShowSuggestions(e.target.value.length > 0)  // 根据输入长度决定是否显示建议
  }

  // 处理键盘事件，实现键盘导航功能
  const handleKeyDown = (e) => {
    // 如果没有建议或建议列表为空，直接返回
    if (!showSuggestions || filteredSuggestions.length === 0) return

    switch (e.key) {
      case 'ArrowDown':  // 向下箭头键
        e.preventDefault()  // 阻止默认行为（防止光标移动）
        setSelectedIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : prev  // 向下选择，但不超出范围
        )
        break
      case 'ArrowUp':    // 向上箭头键
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)  // 向上选择，-1表示没有选中
        break
      case 'Enter':      // 回车键
        if (selectedIndex >= 0) {  // 如果有选中的建议
          e.preventDefault()
          setQuery(filteredSuggestions[selectedIndex])  // 将选中的建议填入输入框
          setShowSuggestions(false)  // 隐藏建议列表
          setSelectedIndex(-1)       // 重置选中索引
        }
        break
      case 'Escape':     // ESC键
        setShowSuggestions(false)  // 隐藏建议列表
        setSelectedIndex(-1)       // 重置选中索引
        break
    }
  }

  const selectSuggestion = (suggestion) => {
    setQuery(suggestion)
    setShowSuggestions(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  const clearInput = () => {
    setQuery('')
    setShowSuggestions(false)
    inputRef.current?.focus()
  }

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (inputRef.current && !inputRef.current.contains(event.target)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={`relative w-full max-w-2xl ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative flex items-center bg-white border border-gray-300 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200 focus-within:border-primary-500 focus-within:shadow-xl">
          <Search className="w-5 h-5 text-gray-400 ml-6" />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => query.length > 0 && setShowSuggestions(true)}
            placeholder={placeholder}
            className="flex-1 px-4 py-4 text-lg bg-transparent border-none outline-none"
          />
          {query && (
            <button
              type="button"
              onClick={clearInput}
              className="p-2 mr-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
          <button
            type="submit"
            className="px-6 py-2 mr-2 bg-primary-500 text-white rounded-full hover:bg-primary-600 transition-colors duration-200 disabled:opacity-50"
            disabled={!query.trim()}
          >
            搜索
          </button>
        </div>
      </form>

      {/* 搜索建议下拉框 */}
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={index}
              onClick={() => selectSuggestion(suggestion)}
              className={`flex items-center px-4 py-3 cursor-pointer transition-colors ${
                index === selectedIndex 
                  ? 'bg-primary-50 text-primary-600' 
                  : 'hover:bg-gray-50'
              }`}
            >
              <Search className="w-4 h-4 text-gray-400 mr-3" />
              <span>{suggestion}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default SearchInput 