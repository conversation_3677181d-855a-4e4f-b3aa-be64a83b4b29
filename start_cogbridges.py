#!/usr/bin/env python3
"""
CogBridges - 统一启动脚本
同时启动后端API服务器和前端Web界面的单一启动脚本
"""

import sys
import os
import subprocess
import threading
import time
import webbrowser
from pathlib import Path
import signal
import atexit
import socket
import psutil

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger
from api.app import create_app, get_cogbridges_service


class CogBridgesLauncher:
    """CogBridges统一启动器"""
    
    def __init__(self):
        """初始化启动器"""
        self.logger = get_logger(__name__)
        self.backend_process = None
        self.frontend_server = None
        self.frontend_process = None  # React前端进程
        self.cogbridges_service = None  # CogBridges服务实例
        self.running = False
        
        # 注册退出处理
        atexit.register(self.cleanup)

        # 只在主线程中注册信号处理器
        try:
            import threading
            if threading.current_thread() is threading.main_thread():
                signal.signal(signal.SIGINT, self.signal_handler)
                signal.signal(signal.SIGTERM, self.signal_handler)
                self.logger.debug("信号处理器已在主线程中注册")
            else:
                self.logger.debug("非主线程，跳过信号处理器注册")
        except Exception as e:
            self.logger.warning(f"信号处理器注册失败: {e}")
    
    def check_and_clean_port(self, port, host="localhost"):
        """检查端口占用并清理"""
        print(f"🔍 检查端口 {host}:{port} 占用情况...")
        
        try:
            # 检查端口是否被占用
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ 端口 {port} 已被占用，正在查找占用进程...")
                
                # 查找占用端口的进程
                processes_using_port = []
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        connections = proc.connections()
                        for conn in connections:
                            if conn.laddr.port == port:
                                processes_using_port.append({
                                    'pid': proc.pid,
                                    'name': proc.name(),
                                    'cmdline': ' '.join(proc.cmdline()) if proc.cmdline() else ''
                                })
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue
                
                if processes_using_port:
                    print(f"📋 发现 {len(processes_using_port)} 个进程占用端口 {port}:")
                    for proc_info in processes_using_port:
                        print(f"   - PID: {proc_info['pid']}, 名称: {proc_info['name']}")
                        if proc_info['cmdline']:
                            print(f"     命令: {proc_info['cmdline'][:100]}...")
                    
                    # 询问是否清理端口
                    try:
                        response = input(f"\n❓ 是否终止占用端口 {port} 的进程？(y/N): ").strip().lower()
                        if response in ['y', 'yes']:
                            killed_count = 0
                            for proc_info in processes_using_port:
                                try:
                                    proc = psutil.Process(proc_info['pid'])
                                    proc.terminate()
                                    proc.wait(timeout=5)
                                    print(f"✅ 已终止进程 PID {proc_info['pid']} ({proc_info['name']})")
                                    killed_count += 1
                                except (psutil.NoSuchProcess, psutil.TimeoutExpired, psutil.AccessDenied) as e:
                                    print(f"⚠️ 无法终止进程 PID {proc_info['pid']}: {e}")
                            
                            if killed_count > 0:
                                print(f"✅ 已清理 {killed_count} 个占用端口的进程")
                                # 等待端口释放
                                time.sleep(2)
                                return True
                            else:
                                print("❌ 未能清理任何进程")
                                return False
                        else:
                            print("⏭️ 跳过端口清理")
                            return False
                    except KeyboardInterrupt:
                        print("\n⏭️ 用户取消，跳过端口清理")
                        return False
                else:
                    print(f"⚠️ 端口 {port} 被占用但无法识别占用进程")
                    return False
            else:
                print(f"✅ 端口 {port} 可用")
                return True
                
        except Exception as e:
            self.logger.error(f"检查端口 {port} 时出错: {e}")
            print(f"⚠️ 检查端口 {port} 时出错: {e}")
            return False
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 定义包名和对应的导入名
        package_mappings = {
            'flask': 'flask',
            'flask_cors': 'flask_cors', 
            'requests': 'requests',
            'praw': 'praw',
            'asyncpraw': 'asyncpraw',
            'tenacity': 'tenacity',
            'python-dotenv': 'dotenv',
            'psutil': 'psutil'  # 添加psutil依赖
        }
        
        missing_packages = []
        
        for package, import_name in package_mappings.items():
            try:
                __import__(import_name)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少以下依赖包:")
            for package in missing_packages:
                print(f"   - {package}")
            print(f"\n💡 请运行以下命令安装依赖:")
            print(f"   pip install -r requirements.txt")
            return False
        
        print("✅ 所有依赖已安装")
        return True
    
    def check_configuration(self):
        """检查配置"""
        print("🔧 检查配置...")
        
        # 检查必要的配置项
        required_configs = [
            ('GOOGLE_API_KEY', config.GOOGLE_API_KEY),
            ('GOOGLE_SEARCH_ENGINE_ID', config.GOOGLE_SEARCH_ENGINE_ID),
            ('REDDIT_CLIENT_ID', config.REDDIT_CLIENT_ID),
            ('REDDIT_CLIENT_SECRET', config.REDDIT_CLIENT_SECRET)
        ]
        
        missing_configs = []
        
        for name, value in required_configs:
            if not value or value == "your_api_key_here":
                missing_configs.append(name)
        
        if missing_configs:
            print(f"❌ 缺少以下配置:")
            for config_name in missing_configs:
                print(f"   - {config_name}")
            print(f"\n💡 请在config.py中配置这些参数")
            return False
        
        # 检查LLM服务配置（可选）
        if not config.replicate_configured:
            print("⚠️ Replicate API未配置，LLM分析功能将不可用")
        else:
            print("✅ LLM服务配置检查通过")
        
        # 检查日志配置
        print("📝 日志配置:")
        print(f"   - 日志级别: {config.LOG_LEVEL}")
        print(f"   - 详细日志保存: {'✅ 已启用' if config.SAVE_DETAILED_LOGS else '❌ 已禁用'}")
        print(f"   - JSON日志格式: {'✅ 已启用' if config.ENABLE_JSON_LOGS else '❌ 已禁用'}")
        print(f"   - 日志目录: {config.LOGS_DIR}")
        
        # 检查数据库配置
        print("💾 数据库配置:")
        print(f"   - 数据库功能: {'✅ 已启用' if config.ENABLE_DATABASE else '❌ 已禁用'}")
        if config.ENABLE_DATABASE:
            if config.database_configured:
                print(f"   - 数据库连接: ✅ 已配置")
                print(f"   - 数据库URL: {config.database_url[:50]}..." if len(config.database_url) > 50 else f"   - 数据库URL: {config.database_url}")
                print(f"   - 连接池大小: {config.DB_POOL_SIZE}")
                print(f"   - 最大溢出: {config.DB_MAX_OVERFLOW}")
                print(f"   - 连接超时: {config.DB_POOL_TIMEOUT}秒")
            else:
                print(f"   - 数据库连接: ❌ 未配置")
        else:
            print(f"   - 数据库功能已禁用，将使用JSON文件存储")
        
        # 检查JSON备份配置
        print("📄 JSON备份配置:")
        print(f"   - JSON备份功能: {'✅ 已启用' if config.ENABLE_JSON_BACKUP else '❌ 已禁用'}")
        if config.ENABLE_JSON_BACKUP:
            print(f"   - 结果目录: {config.RESULTS_DIR}")
        
        print("✅ 配置检查通过")
        return True
    

    

    

    
    def start_backend_api(self):
        """启动后端API服务器"""
        print("🚀 启动后端API服务器...")
        
        try:
            # 使用独立的API服务器启动脚本
            from api.app import create_app
            
            # 检查并清理端口占用
            if not self.check_and_clean_port(config.PORT, config.HOST):
                print(f"⚠️ 端口 {config.PORT} 清理失败，尝试使用其他端口")
                config.PORT = config.PORT + 10
                print(f"🔄 改用端口: {config.PORT}")
                # 再次检查新端口
                if not self.check_and_clean_port(config.PORT, config.HOST):
                    print(f"❌ 端口 {config.PORT} 也无法使用，启动失败")
                    return False
            
            # 创建Flask应用
            app = create_app()
            
            # 在单独线程中启动Flask应用
            def run_flask():
                try:
                    print(f"🔧 Flask配置: host={config.HOST}, port={config.PORT}")
                    app.run(
                        host=config.HOST,
                        port=config.PORT,
                        debug=False,  # 关闭调试模式避免重复启动
                        threaded=True,
                        use_reloader=False  # 关闭reloader避免线程冲突
                    )
                except Exception as e:
                    print(f"❌ Flask应用启动失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            # 等待服务器启动
            print("⏳ 等待Flask服务器启动...")
            time.sleep(8)  # 增加等待时间
            
            # 测试API是否正常
            import requests
            
            # 首先测试简单路由
            try:
                print(f"🔍 正在测试简单路由: http://{config.HOST}:{config.PORT}/test")
                test_response = requests.get(f"http://{config.HOST}:{config.PORT}/test", timeout=10)
                print(f"📡 简单路由响应状态码: {test_response.status_code}")
                if test_response.status_code == 200:
                    print(f"📄 简单路由响应内容: {test_response.text}")
                    print("✅ 简单路由测试成功")
                else:
                    print(f"❌ 简单路由响应异常: {test_response.status_code}")
                    print(f"📄 响应内容: {test_response.text[:200]}...")
            except requests.RequestException as e:
                print(f"❌ 简单路由连接失败: {e}")
                return False
            
            # 然后测试健康检查路由
            try:
                print(f"🔍 正在测试API连接: http://{config.HOST}:{config.PORT}/api/health")
                response = requests.get(f"http://{config.HOST}:{config.PORT}/api/health", timeout=10)
                print(f"📡 API响应状态码: {response.status_code}")
                if response.status_code == 200:
                    print(f"✅ 后端API服务器启动成功: http://{config.HOST}:{config.PORT}")
                    return True
                else:
                    print(f"❌ 后端API服务器响应异常: {response.status_code}")
                    print(f"📄 响应内容: {response.text[:200]}...")
                    return False
            except requests.RequestException as e:
                print(f"❌ 后端API服务器连接失败: {e}")
                print(f"💡 可能的原因: 端口被占用、防火墙阻止、或服务器启动失败")
                return False
                
        except Exception as e:
            self.logger.error(f"后端API服务器启动失败: {e}")
            print(f"❌ 后端API服务器启动失败: {e}")
            return False
    
    def start_frontend_server(self):
        """启动React前端开发服务器"""
        print("🌐 启动React前端开发服务器...")
        
        try:
            import subprocess
            import sys
            import os
            
            # 设置前端目录
            frontend_dir = project_root / "frontend"
            if not frontend_dir.exists():
                print(f"❌ React前端目录不存在: {frontend_dir}")
                print("💡 请先运行 'npm create vite@latest frontend -- --template react' 创建前端项目")
                return False
            
            # 检查package.json是否存在
            package_json = frontend_dir / "package.json"
            if not package_json.exists():
                print(f"❌ 前端项目未初始化: {package_json}")
                print("💡 请先在前端目录运行 'npm install' 安装依赖")
                return False
            
            # 检查node_modules是否存在
            node_modules = frontend_dir / "node_modules"
            if not node_modules.exists():
                print("📦 检测到前端依赖未安装，正在安装...")
                try:
                    result = subprocess.run(
                        "npm install",
                        cwd=str(frontend_dir),
                        capture_output=True,
                        text=True,
                        timeout=120,
                        shell=True
                    )
                    if result.returncode != 0:
                        print(f"❌ 前端依赖安装失败: {result.stderr}")
                        return False
                    print("✅ 前端依赖安装完成")
                except subprocess.TimeoutExpired:
                    print("❌ 前端依赖安装超时")
                    return False
                except Exception as e:
                    print(f"❌ 前端依赖安装失败: {e}")
                    return False
            
            # 前端端口为API端口+1
            frontend_port = config.PORT + 1
            
            # 检查并清理前端端口占用
            if not self.check_and_clean_port(frontend_port, "localhost"):
                print(f"⚠️ 前端端口 {frontend_port} 清理失败，尝试使用其他端口")
                frontend_port = frontend_port + 1
                print(f"🔄 改用前端端口: {frontend_port}")
                # 再次检查新端口
                if not self.check_and_clean_port(frontend_port, "localhost"):
                    print(f"⚠️ 前端端口 {frontend_port} 也无法使用，跳过前端服务器启动")
                    return True  # 返回True避免阻止整体启动
            
            def run_frontend_server():
                try:
                    # 启动Vite开发服务器
                    env = os.environ.copy()
                    env['VITE_API_URL'] = f"http://localhost:{config.PORT}"
                    
                    # 使用shell=True来确保能找到npm命令
                    cmd = f"npm run dev -- --port {frontend_port} --host"
                    process = subprocess.Popen(
                        cmd,
                        cwd=str(frontend_dir),
                        env=env,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True,
                        shell=True
                    )
                    
                    self.frontend_process = process
                    print(f"✅ React前端开发服务器启动成功: http://localhost:{frontend_port}")
                    
                    # 等待进程结束
                    while self.running and process.poll() is None:
                        time.sleep(1)
                        
                except Exception as e:
                    print(f"⚠️ React前端服务器启动失败: {e}")
            
            frontend_thread = threading.Thread(target=run_frontend_server, daemon=True)
            frontend_thread.start()
            
            # 等待服务器启动
            time.sleep(3)
            return True
            
        except Exception as e:
            self.logger.error(f"React前端服务器启动失败: {e}")
            print(f"❌ React前端服务器启动失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        frontend_port = config.PORT + 1
        url = f"http://localhost:{frontend_port}"
        
        print(f"🌐 正在打开浏览器: {url}")
        
        try:
            # 检查前端服务是否可用
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("localhost", frontend_port))
            sock.close()
            
            if result == 0:
                webbrowser.open(url)
                print("✅ 浏览器已打开")
            else:
                print(f"⚠️ 前端服务器未启动，请手动访问: {url}")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"💡 请手动访问: {url}")
    
    def display_startup_info(self):
        """显示启动信息"""
        print("\n" + "=" * 60)
        print("🎉 CogBridges 启动成功！")
        print("=" * 60)
        print(f"🔗 后端API地址: http://{config.HOST}:{config.PORT}")
        print(f"🌐 前端Web地址: http://localhost:{config.PORT + 1}")
        print(f"📚 API文档: http://{config.HOST}:{config.PORT}/api/health")
        print("-" * 60)
        print("💡 使用说明:")
        print("  1. 在Web界面中输入搜索查询")
        print("  2. 系统将自动执行Google搜索 → Reddit数据获取 → 用户分析")
        print("  3. 执行LLM相似subreddit筛选和用户评论动机分析")
        print("  4. 查看完整的分析结果和用户画像")
        print("-" * 60)
        print("🧠 LLM分析功能:")
        if config.replicate_configured:
            print("  ✅ 相似subreddit筛选: 已启用")
            print("  ✅ 用户评论动机分析: 已启用")
        else:
            print("  ⚠️ LLM分析功能: 未配置Replicate API")
        print("-" * 60)
        print("📊 数据存储配置:")
        print(f"  📝 日志级别: {config.LOG_LEVEL}")
        print(f"  📄 详细日志: {'✅ 已启用' if config.SAVE_DETAILED_LOGS else '❌ 已禁用'}")
        print(f"  📄 JSON日志: {'✅ 已启用' if config.ENABLE_JSON_LOGS else '❌ 已禁用'}")
        print(f"  💾 数据库存储: {'✅ 已启用' if config.ENABLE_DATABASE else '❌ 已禁用'}")
        print(f"  📄 JSON备份: {'✅ 已启用' if config.ENABLE_JSON_BACKUP else '❌ 已禁用'}")
        print("-" * 60)
        print("🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
    
    def run(self):
        """运行启动器"""
        print("🌟 CogBridges 统一启动器")
        print("=" * 60)
        
        # 检查依赖和配置
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # 创建Flask应用
        flask_app, self.cogbridges_service = self.start_backend_api_direct()
        if not flask_app:
            return False
        
        # 启动前端Web服务器
        if not self.start_frontend_server():
            return False
        
        # 显示启动信息
        self.display_startup_info()
        
        # 打开浏览器
        self.open_browser()
        
        # 启动Flask应用
        print(f"🚀 正在启动Flask服务器: {config.HOST}:{config.PORT}")
        print("⏳ Flask应用即将启动，请稍候...")
        
        try:
            flask_app.run(
                host=config.HOST,
                port=config.PORT,
                debug=False,  # 关闭调试模式避免重复启动
                threaded=True
            )
        except Exception as e:
            print(f"❌ Flask应用运行失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
    
    def start_backend_api_direct(self):
        """直接启动后端API服务器（非线程模式）"""
        print("🚀 启动后端API服务器（直接模式）...")
        
        try:
            # 创建Flask应用和CogBridges服务
            app = create_app()
            service = get_cogbridges_service()
            return app, service
                
        except Exception as e:
            self.logger.error(f"后端API服务器启动失败: {e}")
            print(f"❌ 后端API服务器启动失败: {e}")
            return None, None
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 正在清理资源...")
        self.running = False
        
        # 清理CogBridges服务
        if self.cogbridges_service:
            try:
                # 使用asyncio.run来运行异步的close方法
                import asyncio
                asyncio.run(self.cogbridges_service.close())
                print("✅ CogBridges服务已清理")
            except Exception as e:
                print(f"⚠️ CogBridges服务清理时出错: {e}")
        
        # 清理React前端进程
        if hasattr(self, 'frontend_process') and self.frontend_process:
            try:
                # 终止React开发服务器进程
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ React前端服务器已停止")
            except Exception as e:
                print(f"⚠️ React前端服务器关闭时出错: {e}")
        
        # 清理旧的前端服务器（兼容性）
        if hasattr(self, 'frontend_server') and self.frontend_server:
            try:
                self.frontend_server.shutdown()
                self.frontend_server.server_close()
                print("✅ 旧前端服务器已停止")
            except Exception as e:
                print(f"⚠️ 旧前端服务器关闭时出错: {e}")
        
        # 清理后端进程
        if hasattr(self, 'backend_process') and self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ 后端进程已停止")
            except Exception as e:
                print(f"⚠️ 后端进程关闭时出错: {e}")
        
        print("👋 CogBridges 已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在停止服务...")
        
        # 设置超时清理
        import threading
        cleanup_thread = threading.Thread(target=self.cleanup, daemon=True)
        cleanup_thread.start()
        
        # 等待清理完成或超时
        cleanup_thread.join(timeout=3)
        
        # 强制退出
        print("🚪 强制退出...")
        os._exit(0)


def main():
    """主函数"""
    launcher = CogBridgesLauncher()
    
    try:
        success = launcher.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止...")
        launcher.cleanup()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
