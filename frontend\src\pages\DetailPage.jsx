import { useState, useEffect } from 'react'
import { useParams, useNavigate, useLocation } from 'react-router-dom'
import { ArrowLeft, Share2, Bookmark, ExternalLink } from 'lucide-react'
import PersonaTag from '../components/PersonaTag'
import InsightPanel from '../components/InsightPanel'
import { api } from '../services/api'

const DetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const location = useLocation()
  
  const [result, setResult] = useState(null)
  const [userInsights, setUserInsights] = useState(null)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  const query = location.state?.query || '未知查询'

  // 生成用户洞察数据（优先使用后端 LLM 分析结果）
  const generateUserInsights = (result) => {
    if (!result) return null

    const backendData = result._rawData?.backendData
    const author = result.author

    // 尝试从后端返回的 LLM 分析中提取
    let personalityAnalysis = null
    let motivationAnalysis = null
    let whyRecommended = null

    if (backendData?.llm_analysis?.motivation_analysis) {
      const userAnalyses = backendData.llm_analysis.motivation_analysis[author]
      if (userAnalyses && userAnalyses.length > 0) {
        const analysis = userAnalyses[0]
        personalityAnalysis =
          analysis.overall_assessment || analysis.professional_background || null
        motivationAnalysis = analysis.participation_motivation || null
        whyRecommended =
          analysis.matching_value || analysis.user_profile || null
      }
    }

    // 后备方案：如果没有 LLM 数据，则使用结果自带的 fields
    if (!personalityAnalysis) {
      personalityAnalysis = result.insights || '该用户提供了具有参考价值的观点。'
    }
    if (!whyRecommended) {
      whyRecommended = result.recommendation || '基于发言质量与相关性推荐该用户。'
    }

    return {
      username: author,
      personalityAnalysis,
      motivationAnalysis,
      whyRecommended
    }
  }

  useEffect(() => {
    const fetchResult = async () => {
      try {
        setIsLoading(true)
        
        // 从本地存储获取结果
        const storedResults = localStorage.getItem('cogbridges_results')
        if (storedResults) {
          const results = JSON.parse(storedResults)
          const foundResult = results.find(r => r.id === id || r._id === id)
          if (foundResult) {
            setResult(foundResult)
            setUserInsights(generateUserInsights(foundResult))
            return
          }
        }

        // 如果本地没有找到，尝试从location.state获取
        const locationResult = location.state?.result
        if (locationResult && (locationResult.id === id || locationResult._id === id)) {
          setResult(locationResult)
          setUserInsights(generateUserInsights(locationResult))
          return
        }

        // 如果都没有找到，显示错误
        setError('无法找到对应的结果数据')
      } catch (err) {
        console.error('获取结果失败:', err)
        setError('无法加载结果数据')
      } finally {
        setIsLoading(false)
      }
    }

    if (id) {
      fetchResult()
    }
  }, [id, location.state])

  const handleBack = () => {
    // 按浏览器历史返回，确保保留ResultsPage的状态
    navigate(-1)
  }

  const handleBookmark = async () => {
    try {
      if (isBookmarked) {
        // 取消收藏
        await api.removeBookmark(id)
        setIsBookmarked(false)
      } else {
        // 添加收藏
        await api.addBookmark(id)
        setIsBookmarked(true)
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
    }
  }

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `CogBridges - ${query}`,
          text: `查看这个来自Reddit的精彩回答`,
          url: window.location.href
        })
      } else {
        // 复制链接到剪贴板
        await navigator.clipboard.writeText(window.location.href)
        alert('链接已复制到剪贴板')
      }
    } catch (error) {
      console.error('分享失败:', error)
    }
  }

  const getCredibilityScore = () => {
    const karma = result?.total_karma || result?.karma || 0
    const score = result?.score || 0
    
    let credibility = 50 // 基础分数
    credibility += Math.min(karma / 1000, 30) // karma贡献
    credibility += Math.min(score, 20) // 评分贡献
    
    return Math.min(Math.round(credibility), 100)
  }

  const getRelevanceScore = () => {
    const commentLength = (result?.body || result?.comment || '').length
    const score = result?.score || 0
    
    let relevance = 60 // 基础分数
    relevance += Math.min(commentLength / 50, 25) // 长度贡献
    relevance += Math.min(score, 15) // 评分贡献
    
    return Math.min(Math.round(relevance), 100)
  }

  const getUsefulnessScore = () => {
    const commentLength = (result?.body || result?.comment || '').length
    const score = result?.score || 0
    
    let usefulness = 65 // 基础分数
    usefulness += Math.min(commentLength / 20, 20) // 长度贡献
    usefulness += Math.min(score, 15) // 评分贡献
    
    return Math.min(Math.round(usefulness), 100)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || '结果不存在'}</p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
          >
            返回结果页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              
              <div>
                <h1 className="text-lg font-semibold text-gray-800">回答详情</h1>
                <p className="text-sm text-gray-500">来自 Reddit 社区</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleShare}
                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="分享"
              >
                <Share2 className="w-5 h-5" />
              </button>

              <button
                onClick={handleBookmark}
                className={`p-2 rounded-lg transition-colors ${
                  isBookmarked
                    ? 'text-yellow-600 bg-yellow-50 hover:bg-yellow-100'
                    : 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50'
                }`}
                title={isBookmarked ? '取消收藏' : '收藏'}
              >
                <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧 - 原始回答内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 原问题上下文 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800 mb-2">原问题</h3>
              <p className="text-blue-700">"{query}"</p>
            </div>

            {/* 回答内容 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-semibold">
                      {result.author.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">u/{result.author}</h3>
                    <p className="text-sm text-gray-500">来自 r/{result.subreddit}</p>
                  </div>
                </div>
                
                <div className="text-right text-sm text-gray-500">
                  <div>{result.score} 赞</div>
                </div>
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-4">
                {result.tags?.map((tag, index) => (
                  <PersonaTag key={index} tag={tag} size="sm" />
                )) || result.recommendation && (
                  <PersonaTag key="recommendation" tag="推荐回答" size="sm" />
                )}
              </div>

              {/* 回答正文 */}
              <div className="prose prose-gray max-w-none">
                <div className="text-gray-700 leading-relaxed text-lg whitespace-pre-wrap">
                  {result._rawData?.comment?.body || result.fullComment || result.body || result.comment}
                </div>
              </div>

              {/* 推荐理由 */}
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">为什么推荐这个回答</h4>
                <p className="text-green-700 text-sm">
                  {result.recommendation || '这是一个来自Reddit社区的高质量回答，获得了用户的认可和好评。'}
                </p>
              </div>

              {/* 原帖链接 */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <a
                  href={result.permalink || result.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 text-sm"
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  查看原帖完整讨论
                </a>
              </div>
            </div>


          </div>

          {/* 右侧 - 用户深度分析 */}
          <div className="lg:col-span-1">
            {userInsights && <InsightPanel userInsights={userInsights} />}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DetailPage 