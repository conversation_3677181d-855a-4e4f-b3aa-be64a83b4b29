/* 根容器样式 - 这是React应用的挂载点 */
#root {
  max-width: 1280px;    /* 最大宽度限制，确保在大屏幕上不会过宽 */
  margin: 0 auto;       /* 水平居中 */
  padding: 2rem;        /* 内边距，2rem = 32px */
  text-align: center;   /* 文本居中对齐 */
}

/* Logo样式 */
.logo {
  height: 6em;                    /* 高度为6em */
  padding: 1.5em;                 /* 内边距1.5em */
  will-change: filter;            /* 告诉浏览器filter属性会变化，优化性能 */
  transition: filter 300ms;       /* filter属性变化时的过渡动画，持续300毫秒 */
}

/* Logo悬停效果 */
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);  /* 悬停时添加紫色阴影效果 */
}

/* React Logo的特殊悬停效果 */
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);  /* React Logo悬停时添加蓝色阴影 */
}

/* 定义Logo旋转动画的关键帧 */
@keyframes logo-spin {
  from {
    transform: rotate(0deg);    /* 动画开始：0度旋转 */
  }
  to {
    transform: rotate(360deg);  /* 动画结束：360度旋转（完整一圈） */
  }
}

/* 媒体查询：检查用户是否偏好减少动画 */
@media (prefers-reduced-motion: no-preference) {
  /* 如果用户没有设置减少动画偏好，则应用旋转动画 */
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;  /* 无限循环，20秒一圈，线性动画 */
  }
}

/* 卡片样式 */
.card {
  padding: 2em;  /* 卡片内边距 */
}

/* 文档链接样式 */
.read-the-docs {
  color: #888;   /* 灰色文字 */
}
