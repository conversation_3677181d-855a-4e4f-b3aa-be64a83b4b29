#!/usr/bin/env python3
"""
测试智能搜索功能
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.cogbridges_service import CogBridgesService
from utils.logger_utils import get_logger

logger = get_logger(__name__)

async def test_intelligent_search():
    """测试智能搜索功能"""
    
    # 测试查询
    test_queries = [
        "startup如何平衡产品设计和用户验证的时间成本",
        "人工智能对就业市场的影响",
        "远程工作的优缺点",
        "如何学习编程",
        "投资理财建议"
    ]
    
    try:
        # 初始化CogBridges服务
        logger.info("初始化CogBridges服务...")
        service = CogBridgesService()
        
        for i, query in enumerate(test_queries, 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"测试查询 {i}/{len(test_queries)}: {query}")
            logger.info(f"{'='*60}")
            
            try:
                # 执行搜索
                start_time = datetime.now()
                result = await service.search(query, save_to_db=False)
                end_time = datetime.now()
                
                # 输出结果统计
                logger.info(f"\n搜索结果统计:")
                logger.info(f"- 原始查询: {result.query}")
                logger.info(f"- 优化查询: {result.translated_query}")
                logger.info(f"- 搜索成功: {result.success}")
                logger.info(f"- Google结果数: {len(result.google_results) if result.google_results else 0}")
                logger.info(f"- Reddit帖子数: {len(result.reddit_posts) if result.reddit_posts else 0}")
                logger.info(f"- 总耗时: {(end_time - start_time).total_seconds():.2f}秒")
                
                if result.google_results:
                    logger.info(f"\nGoogle搜索结果预览:")
                    for j, google_result in enumerate(result.google_results[:3], 1):
                        logger.info(f"  {j}. {google_result.get('title', 'N/A')}")
                        logger.info(f"     URL: {google_result.get('url', 'N/A')}")
                        logger.info(f"     摘要: {google_result.get('snippet', 'N/A')[:100]}...")
                        if 'search_query' in google_result:
                            logger.info(f"     搜索查询: {google_result['search_query']}")
                        logger.info("")
                
                if result.reddit_posts:
                    logger.info(f"\nReddit帖子预览:")
                    for j, post in enumerate(result.reddit_posts[:2], 1):
                        logger.info(f"  {j}. {post.get('title', 'N/A')}")
                        logger.info(f"     评分: {post.get('score', 'N/A')}")
                        logger.info(f"     评论数: {post.get('num_comments', 'N/A')}")
                        logger.info("")
                
                # 保存详细结果到文件
                result_file = f"test_results/intelligent_search_test_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                os.makedirs("test_results", exist_ok=True)
                
                with open(result_file, 'w', encoding='utf-8') as f:
                    json.dump(result.to_dict(), f, ensure_ascii=False, indent=2)
                
                logger.info(f"详细结果已保存到: {result_file}")
                
            except Exception as e:
                logger.error(f"测试查询 '{query}' 失败: {e}")
                continue
            
            # 在查询之间稍作停顿
            if i < len(test_queries):
                logger.info("等待5秒后进行下一个测试...")
                await asyncio.sleep(5)
        
        logger.info(f"\n{'='*60}")
        logger.info("所有测试完成!")
        logger.info(f"{'='*60}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        raise

async def test_llm_query_generation():
    """单独测试LLM查询生成功能"""
    
    logger.info("\n测试LLM查询生成功能...")
    
    try:
        service = CogBridgesService()
        
        if not service.llm_service or not service.llm_service.configured:
            logger.warning("LLM服务未配置，跳过LLM查询生成测试")
            return
        
        test_query = "startup如何平衡产品设计和用户验证的时间成本"
        
        # 测试智能查询生成
        logger.info(f"原始查询: {test_query}")
        
        queries = await service.llm_service.generate_intelligent_search_queries(test_query)
        logger.info(f"生成的智能查询:")
        for i, query in enumerate(queries, 1):
            logger.info(f"  {i}. {query}")
        
        # 测试相关性评估
        mock_results = [
            {
                "title": "How startups balance product design and user validation",
                "snippet": "Discussion about balancing design time with user testing in early stage startups"
            },
            {
                "title": "Ruby on Rails discussion",
                "snippet": "Some random discussion about Ruby on Rails framework"
            }
        ]
        
        evaluation = await service.llm_service.evaluate_search_results_relevance(test_query, mock_results)
        logger.info(f"相关性评估结果: {evaluation}")
        
    except Exception as e:
        logger.error(f"LLM查询生成测试失败: {e}")

if __name__ == "__main__":
    # 设置日志级别
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 运行测试
    asyncio.run(test_intelligent_search())
    
    # 运行LLM测试
    asyncio.run(test_llm_query_generation())
