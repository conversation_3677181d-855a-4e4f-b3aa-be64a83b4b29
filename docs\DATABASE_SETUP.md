# CogBridges Search - 数据库设置指南

本文档介绍如何设置和使用CogBridges Search的数据库功能。

## 概述

CogBridges Search支持双存储模式：
- **JSON文件存储**：默认模式，将数据保存为JSON文件
- **PostgreSQL数据库**：可选模式，将数据保存到PostgreSQL数据库
- **混合模式**：同时使用数据库和JSON文件备份

## 数据库功能特点

- ✅ 与Render Fully-managed datastores完全兼容
- ✅ 支持本地开发和云端部署
- ✅ 自动数据迁移（从JSON到数据库）
- ✅ 保持与现有JSON数据结构的一致性
- ✅ 支持数据库和JSON文件的双重备份
- ✅ 完整的CRUD操作API

## 快速开始

### 1. 安装依赖

```bash
pip install SQLAlchemy psycopg2-binary alembic
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并配置数据库连接：

```bash
# 启用数据库
ENABLE_DATABASE=True

# 本地开发配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cogbridges_db
DB_USER=postgres
DB_PASSWORD=your_password

# 或者使用DATABASE_URL（Render部署时）
DATABASE_URL=postgresql://user:password@host:port/database
```

### 3. 创建数据库表

```bash
python scripts/init_database.py --create-tables
```

### 4. 迁移现有数据（可选）

```bash
# 迁移所有JSON数据到数据库
python scripts/init_database.py --migrate

# 或迁移指定会话
python scripts/init_database.py --migrate-session SESSION_ID
```

## 本地开发设置

### 1. 安装PostgreSQL

**Windows:**
- 下载并安装 [PostgreSQL](https://www.postgresql.org/download/windows/)
- 创建数据库：`createdb cogbridges_db`

**macOS:**
```bash
brew install postgresql
brew services start postgresql
createdb cogbridges_db
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get install postgresql postgresql-contrib
sudo -u postgres createdb cogbridges_db
```

### 2. 配置数据库用户

```sql
-- 连接到PostgreSQL
psql -U postgres

-- 创建用户和数据库
CREATE USER cogbridges WITH PASSWORD 'your_password';
CREATE DATABASE cogbridges_db OWNER cogbridges;
GRANT ALL PRIVILEGES ON DATABASE cogbridges_db TO cogbridges;
```

### 3. 测试连接

```bash
python scripts/init_database.py --check
```

## Render部署设置

### 1. 创建PostgreSQL数据库

在Render控制台中：
1. 点击 "New +" → "PostgreSQL"
2. 配置数据库名称和区域
3. 创建后获取 `DATABASE_URL`

### 2. 配置环境变量

在Render Web Service中设置：
```
ENABLE_DATABASE=True
DATABASE_URL=<从Render PostgreSQL获取的URL>
ENABLE_JSON_BACKUP=True
```

### 3. 部署后初始化

```bash
# 在Render控制台的Shell中运行
python scripts/init_database.py --create-tables --force
```

## 数据库架构

### 主要表结构

1. **search_sessions** - 搜索会话主表
   - 存储搜索查询、时间戳、统计信息
   - 包含LLM分析状态和JSON备份字段

2. **google_search_results** - Google搜索结果
   - 标题、URL、摘要、排名

3. **reddit_posts** - Reddit帖子
   - 帖子内容、作者、评分、评论数
   - 包含LLM动机分析结果

4. **reddit_comments** - Reddit评论
   - 评论内容、作者、评分、层级关系
   - 包含LLM动机分析结果

5. **user_histories** - 用户历史数据
   - 用户统计信息、历史评论和帖子

6. **subreddit_similarities** - 子版块相似性分析
   - 存储LLM筛选子版块的结果
   - 用户关注的子版块与目标子版块的相似性分析

7. **comment_motivation_analyses** - 评论动机分析
   - 存储LLM分析用户画像的详细结果
   - 专业背景、参与动机、兴趣领域、用户画像、匹配价值等

### LLM分析数据详细结构

#### 子版块相似性分析 (subreddit_similarities)
```sql
CREATE TABLE subreddit_similarities (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(50) REFERENCES search_sessions(id),
    username VARCHAR(100) NOT NULL,
    target_subreddits JSON NOT NULL,        -- 目标子版块列表
    user_subreddits JSON NOT NULL,          -- 用户关注的子版块列表
    similarity_results JSON NOT NULL,       -- 完整的相似性分析结果
    all_similar_subreddits JSON,            -- 所有相似的子版块
    analysis_success BOOLEAN DEFAULT TRUE,
    analysis_error TEXT,
    llm_response_raw TEXT,                   -- LLM原始响应
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 评论动机分析 (comment_motivation_analyses)
```sql
CREATE TABLE comment_motivation_analyses (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(50) REFERENCES search_sessions(id),
    username VARCHAR(100) NOT NULL,
    comment_id VARCHAR(20) REFERENCES reddit_comments(id),
    target_subreddit VARCHAR(100) NOT NULL,
    professional_background TEXT,           -- 专业背景分析
    participation_motivation TEXT,          -- 参与动机分析
    interest_areas TEXT,                     -- 兴趣领域分析
    user_profile TEXT,                       -- 用户画像推断
    matching_value TEXT,                     -- 潜在匹配价值
    overall_assessment TEXT,                 -- 总体评价
    analysis_success BOOLEAN DEFAULT TRUE,
    analysis_error TEXT,
    llm_response_raw TEXT,                   -- LLM原始响应
    target_post_data JSON,                   -- 目标帖子数据
    user_comment_data JSON,                  -- 用户评论数据
    similar_subreddits_data JSON,            -- 相似子版块数据
    user_overview_data JSON,                 -- 用户概览数据
    created_at TIMESTAMP DEFAULT NOW()
);
```

## API接口

### 获取搜索历史
```http
GET /api/history?limit=50
```

### 获取会话详情
```http
GET /api/sessions/{session_id}
```

### 获取会话LLM分析数据
```http
GET /api/sessions/{session_id}/llm-analysis
```

### 删除会话
```http
DELETE /api/sessions/{session_id}
```

### 获取存储统计
```http
GET /api/storage/statistics
```

### 数据迁移
```http
POST /api/database/migrate
Content-Type: application/json

{
  "session_id": "optional_session_id"
}
```

### LLM分析数据示例

#### 子版块相似性分析结果
```json
{
  "similarity_analysis": {
    "developer_user": {
      "target_subreddits": ["ChatGPT", "ClaudeAI"],
      "user_subreddits": ["programming", "Python", "MachineLearning", "ChatGPT"],
      "similarity_results": {
        "ChatGPT": ["programming", "Python", "MachineLearning"],
        "ClaudeAI": ["programming", "Python"]
      },
      "all_similar_subreddits": ["programming", "Python", "MachineLearning"],
      "analysis_success": true,
      "llm_response_raw": "根据分析，用户关注的编程相关子版块与AI工具版块高度相关..."
    }
  }
}
```

#### 评论动机分析结果
```json
{
  "motivation_analysis": {
    "developer_user": [
      {
        "comment_id": "abc123",
        "target_subreddit": "ChatGPT",
        "professional_background": "软件开发者，具有编程经验，熟悉多种AI工具",
        "participation_motivation": "分享技术经验，寻求更好的开发工具",
        "interest_areas": "编程、AI工具、代码生成、技术效率提升",
        "user_profile": "技术型用户，注重实用性，有比较和评估工具的能力",
        "matching_value": "高价值技术伙伴，可以提供实用的AI工具使用建议",
        "overall_assessment": "专业的技术用户，值得深度交流",
        "analysis_success": true
      }
    ]
  }
}
```

## 管理工具

### 数据库初始化脚本

```bash
# 查看帮助
python scripts/init_database.py --help

# 检查数据库状态
python scripts/init_database.py --check

# 创建数据库表
python scripts/init_database.py --create-tables

# 迁移数据
python scripts/init_database.py --migrate

# 显示统计信息
python scripts/init_database.py --stats
```

### 测试脚本

```bash
# 运行集成测试
python test_database_integration.py
```

## 配置选项

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `ENABLE_DATABASE` | `False` | 是否启用数据库存储 |
| `ENABLE_JSON_BACKUP` | `True` | 是否保留JSON文件备份 |
| `DATABASE_URL` | - | 完整数据库连接URL |
| `DB_HOST` | `localhost` | 数据库主机 |
| `DB_PORT` | `5432` | 数据库端口 |
| `DB_NAME` | `cogbridges_db` | 数据库名称 |
| `DB_USER` | `postgres` | 数据库用户 |
| `DB_PASSWORD` | - | 数据库密码 |
| `DB_POOL_SIZE` | `5` | 连接池大小 |
| `DB_MAX_OVERFLOW` | `10` | 最大连接溢出 |

## 故障排除

### 常见问题

1. **连接失败**
   - 检查PostgreSQL服务是否运行
   - 验证连接参数是否正确
   - 确认防火墙设置

2. **权限错误**
   - 确认数据库用户有足够权限
   - 检查数据库是否存在

3. **迁移失败**
   - 检查JSON文件格式是否正确
   - 确认数据库表已创建
   - 查看详细错误日志

### 日志查看

```bash
# 查看应用日志
tail -f data/logs/cogbridges.log

# 查看数据库连接日志
# 在config.py中设置DEBUG_MODE=True
```

## 性能优化

1. **连接池配置**
   - 根据并发需求调整 `DB_POOL_SIZE`
   - 设置合适的 `DB_MAX_OVERFLOW`

2. **索引优化**
   - 数据库表已包含必要索引
   - 可根据查询模式添加自定义索引

3. **数据清理**
   - 定期清理旧的会话数据
   - 使用 `DELETE /api/sessions/{session_id}` API

## 备份和恢复

### 数据备份

```bash
# PostgreSQL备份
pg_dump -U cogbridges cogbridges_db > backup.sql

# JSON文件备份
tar -czf json_backup.tar.gz data/results/
```

### 数据恢复

```bash
# PostgreSQL恢复
psql -U cogbridges cogbridges_db < backup.sql

# JSON文件恢复
tar -xzf json_backup.tar.gz
```

## 更多信息

- [项目主README](../README.md)
- [API文档](./API.md)
- [部署指南](./DEPLOYMENT.md)
