/* 导入Google Fonts的Inter字体，支持多种字重 */
/* 这个字体是现代化的无衬线字体，适合Web应用 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Tailwind CSS的基础样式导入 */
/* @tailwind base: 导入Tailwind的基础样式，重置浏览器默认样式 */
@tailwind base;
/* @tailwind components: 导入Tailwind的组件样式 */
@tailwind components;
/* @tailwind utilities: 导入Tailwind的工具类样式 */
@tailwind utilities;

/* 自定义基础样式层 */
@layer base {
  /* 为html和body元素设置字体 */
  html, body {
    /* 设置字体族，优先使用Inter，然后是系统字体，最后是无衬线字体 */
    font-family: 'Inter', system-ui, sans-serif;
  }
}
