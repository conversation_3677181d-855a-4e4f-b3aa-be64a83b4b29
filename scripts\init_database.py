#!/usr/bin/env python3
"""
CogBridges Search - 数据库初始化脚本
用于创建数据库表和迁移现有JSON数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import argparse
from typing import Dict, Any
from config import config
from services.database_service import DatabaseService
from services.data_service import DataService
from utils.logger_utils import get_logger


def create_tables(database_service: DatabaseService) -> bool:
    """创建数据库表"""
    logger = get_logger(__name__)
    
    try:
        logger.info("开始创建数据库表...")
        database_service.create_tables()
        logger.info("✅ 数据库表创建成功")
        return True
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        return False


def migrate_json_data(data_service: DataService, session_id: str = None) -> Dict[str, Any]:
    """迁移JSON数据到数据库"""
    logger = get_logger(__name__)
    
    try:
        logger.info("开始迁移JSON数据到数据库...")
        results = data_service.migrate_json_to_database(session_id)
        
        if "error" in results:
            logger.error(f"❌ 迁移失败: {results['error']}")
        else:
            logger.info(f"✅ 迁移完成:")
            logger.info(f"   总文件数: {results['total_files']}")
            logger.info(f"   成功迁移: {results['migrated']}")
            logger.info(f"   跳过: {results['skipped']}")
            logger.info(f"   失败: {results['failed']}")
            
            if results['errors']:
                logger.warning("迁移错误:")
                for error in results['errors']:
                    logger.warning(f"   - {error}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 迁移过程失败: {e}")
        return {"error": str(e)}


def check_database_status() -> bool:
    """检查数据库状态"""
    logger = get_logger(__name__)
    
    logger.info("检查数据库配置...")
    
    # 检查配置
    if not config.ENABLE_DATABASE:
        logger.warning("⚠️ 数据库未启用 (ENABLE_DATABASE=False)")
        return False
    
    if not config.database_configured:
        logger.error("数据库未配置")
        logger.error("请设置以下环境变量:")
        logger.error("  - DATABASE_URL (Render部署)")
        logger.error("  或者:")
        logger.error("  - DB_HOST, DB_NAME, DB_USER, DB_PASSWORD (本地开发)")
        return False
    
    logger.info("✅ 数据库配置检查通过")
    
    # 测试连接
    try:
        logger.info("测试数据库连接...")
        database_service = DatabaseService()
        logger.info("✅ 数据库连接成功")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False


def show_statistics():
    """显示存储统计信息"""
    logger = get_logger(__name__)
    
    try:
        data_service = DataService()
        stats = data_service.get_storage_statistics()
        
        logger.info("📊 存储统计信息:")
        logger.info(f"   存储类型: {stats.get('storage_type', 'unknown')}")
        logger.info(f"   数据库启用: {stats.get('database_enabled', False)}")
        logger.info(f"   JSON备份启用: {stats.get('json_backup_enabled', True)}")
        
        # 数据库统计
        if 'database' in stats:
            db_stats = stats['database']
            if 'error' not in db_stats:
                logger.info("   数据库统计:")
                logger.info(f"     会话数: {db_stats.get('sessions_count', 0)}")
                logger.info(f"     Google结果数: {db_stats.get('google_results_count', 0)}")
                logger.info(f"     Reddit帖子数: {db_stats.get('reddit_posts_count', 0)}")
                logger.info(f"     Reddit评论数: {db_stats.get('reddit_comments_count', 0)}")
                logger.info(f"     用户历史数: {db_stats.get('user_histories_count', 0)}")
            else:
                logger.warning(f"   数据库统计错误: {db_stats['error']}")
        
        # JSON文件统计
        if 'json_files' in stats:
            json_stats = stats['json_files']
            if 'error' not in json_stats:
                logger.info("   JSON文件统计:")
                logger.info(f"     总文件数: {json_stats.get('total_files', 0)}")
                logger.info(f"     会话文件数: {json_stats.get('session_count', 0)}")
                logger.info(f"     总大小: {json_stats.get('total_size_mb', 0)} MB")
            else:
                logger.warning(f"   JSON文件统计错误: {json_stats['error']}")
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CogBridges Search 数据库初始化工具")
    parser.add_argument("--check", action="store_true", help="检查数据库状态")
    parser.add_argument("--create-tables", action="store_true", help="创建数据库表")
    parser.add_argument("--migrate", action="store_true", help="迁移JSON数据到数据库")
    parser.add_argument("--migrate-session", type=str, help="迁移指定会话的JSON数据")
    parser.add_argument("--stats", action="store_true", help="显示存储统计信息")
    parser.add_argument("--force", action="store_true", help="强制执行操作（跳过确认）")
    
    args = parser.parse_args()
    
    logger = get_logger(__name__)
    logger.info("CogBridges Search - 数据库初始化工具")
    logger.info("=" * 50)
    
    # 如果没有指定任何操作，显示帮助
    if not any([args.check, args.create_tables, args.migrate, args.migrate_session, args.stats]):
        parser.print_help()
        return
    
    # 检查数据库状态
    if args.check or args.create_tables or args.migrate or args.migrate_session:
        if not check_database_status():
            logger.error("数据库状态检查失败，退出")
            sys.exit(1)
    
    # 创建数据库表
    if args.create_tables:
        if not args.force:
            confirm = input("\n确定要创建数据库表吗？这可能会覆盖现有表结构。(y/N): ")
            if confirm.lower() != 'y':
                logger.info("操作已取消")
                return
        
        database_service = DatabaseService()
        if not create_tables(database_service):
            sys.exit(1)
    
    # 迁移数据
    if args.migrate or args.migrate_session:
        if not args.force:
            session_info = f"会话 {args.migrate_session}" if args.migrate_session else "所有JSON文件"
            confirm = input(f"\n确定要迁移{session_info}到数据库吗？(y/N): ")
            if confirm.lower() != 'y':
                logger.info("操作已取消")
                return
        
        data_service = DataService()
        results = migrate_json_data(data_service, args.migrate_session)
        
        if "error" in results:
            sys.exit(1)
    
    # 显示统计信息
    if args.stats:
        show_statistics()
    
    logger.info("\n✅ 操作完成")


if __name__ == "__main__":
    main()
