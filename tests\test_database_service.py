"""
CogBridges Search - 数据库服务测试
测试数据库服务的各项功能
"""

import pytest
import os
import tempfile
from datetime import datetime
from unittest.mock import patch, MagicMock

# 设置测试环境变量
os.environ["ENABLE_DATABASE"] = "True"
os.environ["ENABLE_JSON_BACKUP"] = "True"
os.environ["TEST_MODE"] = "True"

from services.database_service import DatabaseService
from services.data_service import DataService
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from models.database_models import Base
from config import config


class TestDatabaseService:
    """数据库服务测试类"""
    
    @pytest.fixture
    def mock_database_service(self):
        """创建模拟的数据库服务"""
        with patch('services.database_service.create_engine') as mock_engine:
            with patch('services.database_service.sessionmaker') as mock_session:
                # 模拟数据库连接成功
                mock_engine.return_value.connect.return_value.__enter__.return_value.execute.return_value = None
                
                service = DatabaseService()
                service.engine = mock_engine.return_value
                service.SessionLocal = mock_session.return_value
                
                yield service
    
    @pytest.fixture
    def sample_search_result(self):
        """创建示例搜索结果"""
        query = SearchQuery(
            query="test query",
            timestamp=datetime.now(),
            search_type="reddit",
            max_results=5
        )
        
        result = SearchResult(
            query=query,
            success=True
        )
        
        # 添加Google搜索结果
        google_result = GoogleSearchResult(
            title="Test Title",
            url="https://example.com",
            snippet="Test snippet",
            display_url="example.com",
            rank=1
        )
        result.google_results = [google_result]
        
        return result
    
    @pytest.fixture
    def sample_reddit_data(self):
        """创建示例Reddit数据"""
        return {
            "reddit_posts": [
                {
                    "id": "test_post_1",
                    "title": "Test Post Title",
                    "selftext": "Test post content",
                    "author": "test_user",
                    "score": 100,
                    "num_comments": 10,
                    "created_utc": 1640995200.0,
                    "subreddit": "test",
                    "permalink": "/r/test/comments/test_post_1/",
                    "url": "https://reddit.com/r/test/comments/test_post_1/"
                }
            ],
            "reddit_comments": [
                {
                    "id": "test_comment_1",
                    "body": "Test comment content",
                    "author": "test_commenter",
                    "score": 50,
                    "created_utc": **********.0,
                    "parent_id": "test_post_1",
                    "subreddit": "test",
                    "permalink": "/r/test/comments/test_post_1/test_comment_1/"
                }
            ],
            "user_histories": [
                {
                    "username": "test_user",
                    "total_comments": 100,
                    "total_posts": 20,
                    "account_created_utc": **********.0,
                    "comments_data": [],
                    "posts_data": []
                }
            ]
        }
    
    def test_database_service_initialization(self):
        """测试数据库服务初始化"""
        with patch('services.database_service.create_engine') as mock_engine:
            with patch('services.database_service.sessionmaker') as mock_session:
                # 模拟数据库连接成功
                mock_engine.return_value.connect.return_value.__enter__.return_value.execute.return_value = None
                
                service = DatabaseService()
                
                assert service.engine is not None
                assert service.SessionLocal is not None
                mock_engine.assert_called_once()
                mock_session.assert_called_once()
    
    def test_database_service_initialization_failure(self):
        """测试数据库服务初始化失败"""
        with patch('services.database_service.create_engine') as mock_engine:
            # 模拟数据库连接失败
            mock_engine.side_effect = Exception("Database connection failed")
            
            with pytest.raises(Exception):
                DatabaseService()
    
    def test_create_tables(self, mock_database_service):
        """测试创建数据库表"""
        with patch.object(Base.metadata, 'create_all') as mock_create:
            mock_database_service.create_tables()
            mock_create.assert_called_once_with(bind=mock_database_service.engine)
    
    def test_save_search_session(self, mock_database_service, sample_search_result, sample_reddit_data):
        """测试保存搜索会话"""
        session_id = "test_session_123"
        
        # 模拟数据库会话
        mock_session = MagicMock()
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        result = mock_database_service.save_search_session(
            session_id=session_id,
            search_result=sample_search_result,
            reddit_data=sample_reddit_data
        )
        
        assert result is True
        mock_session.add.assert_called()
        mock_session.commit.assert_called()
    
    def test_load_search_session(self, mock_database_service):
        """测试加载搜索会话"""
        session_id = "test_session_123"
        
        # 模拟数据库查询结果
        mock_session = MagicMock()
        mock_search_session = MagicMock()
        mock_search_session.to_dict.return_value = {
            "id": session_id,
            "query": "test query",
            "success": True
        }
        
        mock_session.query.return_value.filter.return_value.first.return_value = mock_search_session
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = []
        mock_session.query.return_value.filter.return_value.all.return_value = []
        
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        result = mock_database_service.load_search_session(session_id)
        
        assert result is not None
        assert result["id"] == session_id
        assert result["query"] == "test query"
    
    def test_delete_search_session(self, mock_database_service):
        """测试删除搜索会话"""
        session_id = "test_session_123"
        
        # 模拟数据库查询和删除
        mock_session = MagicMock()
        mock_search_session = MagicMock()
        
        mock_session.query.return_value.filter.return_value.first.return_value = mock_search_session
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        result = mock_database_service.delete_search_session(session_id)
        
        assert result is True
        mock_session.delete.assert_called_with(mock_search_session)
        mock_session.commit.assert_called()
    
    def test_get_database_statistics(self, mock_database_service):
        """测试获取数据库统计"""
        # 模拟数据库查询结果
        mock_session = MagicMock()
        mock_session.query.return_value.count.return_value = 10
        mock_session.query.return_value.order_by.return_value.first.return_value = None
        
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        stats = mock_database_service.get_database_statistics()
        
        assert "database_enabled" in stats
        assert "sessions_count" in stats
        assert stats["sessions_count"] == 10
    
    def test_is_available(self, mock_database_service):
        """测试数据库服务可用性检查"""
        assert mock_database_service.is_available() is True
        
        # 测试不可用情况
        mock_database_service.engine = None
        assert mock_database_service.is_available() is False


class TestDataServiceWithDatabase:
    """测试DataService与数据库的集成"""
    
    @pytest.fixture
    def mock_data_service(self):
        """创建模拟的数据服务"""
        with patch('services.data_service.DatabaseService') as mock_db_service:
            mock_db_instance = MagicMock()
            mock_db_instance.is_available.return_value = True
            mock_db_service.return_value = mock_db_instance
            
            service = DataService()
            service.database_service = mock_db_instance
            
            yield service, mock_db_instance
    
    def test_save_complete_session_with_database(self, mock_data_service, sample_search_result):
        """测试使用数据库保存完整会话"""
        data_service, mock_db_service = mock_data_service
        
        session_id = "test_session_123"
        data_to_save = {
            "session_id": session_id,
            "search_result": sample_search_result.to_dict(),
            "reddit_data": {},
            "timestamp": datetime.now().isoformat()
        }
        
        mock_db_service.save_search_session.return_value = True
        
        with patch('builtins.open', create=True) as mock_open:
            with patch('json.dump') as mock_json_dump:
                result = data_service.save_complete_session(session_id, data_to_save)
                
                # 验证数据库保存被调用
                mock_db_service.save_search_session.assert_called_once()
                
                # 验证JSON文件也被保存（备份）
                mock_open.assert_called()
                mock_json_dump.assert_called()
                
                assert result is not None
    
    def test_load_session_data_from_database(self, mock_data_service):
        """测试从数据库加载会话数据"""
        data_service, mock_db_service = mock_data_service
        
        session_id = "test_session_123"
        expected_data = {"id": session_id, "query": "test query"}
        
        mock_db_service.load_search_session.return_value = expected_data
        
        result = data_service.load_session_data(session_id)
        
        assert result == expected_data
        mock_db_service.load_search_session.assert_called_once_with(session_id)
    
    def test_get_storage_statistics_hybrid(self, mock_data_service):
        """测试获取混合存储统计"""
        data_service, mock_db_service = mock_data_service
        
        mock_db_service.get_database_statistics.return_value = {
            "sessions_count": 10,
            "database_enabled": True
        }
        
        with patch.object(data_service.results_dir, 'rglob') as mock_rglob:
            with patch.object(data_service.results_dir, 'glob') as mock_glob:
                # 模拟文件系统
                mock_rglob.return_value = []
                mock_glob.return_value = []
                
                stats = data_service.get_storage_statistics()
                
                assert stats["storage_type"] == "hybrid"
                assert "database" in stats
                assert "json_files" in stats
                assert stats["database"]["sessions_count"] == 10


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
