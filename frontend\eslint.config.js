// ESLint配置文件 - ESLint是JavaScript代码质量检查工具
// 用于检测代码中的潜在问题和风格问题

// 导入ESLint的核心配置
import js from '@eslint/js'
// 导入全局变量定义
import globals from 'globals'
// 导入React Hooks的ESLint规则
import reactHooks from 'eslint-plugin-react-hooks'
// 导入React热重载的ESLint规则
import reactRefresh from 'eslint-plugin-react-refresh'
// 导入ESLint配置定义函数和全局忽略配置
import { defineConfig, globalIgnores } from 'eslint/config'

// 导出ESLint配置
export default defineConfig([
  // 全局忽略配置：忽略dist目录（构建输出目录）
  globalIgnores(['dist']),
  
  // 主要配置对象
  {
    // 指定应用此配置的文件类型
    files: ['**/*.{js,jsx}'],  // 所有.js和.jsx文件
    
    // 继承的配置：使用推荐的规则集
    extends: [
      js.configs.recommended,                    // ESLint推荐的基础规则
      reactHooks.configs['recommended-latest'],  // React Hooks的最新推荐规则
      reactRefresh.configs.vite,                 // Vite环境下的React热重载规则
    ],
    
    // 语言选项配置
    languageOptions: {
      ecmaVersion: 2020,        // 使用ES2020语法
      globals: globals.browser, // 浏览器环境的全局变量（如window、document等）
      parserOptions: {
        ecmaVersion: 'latest',  // 使用最新的ECMAScript版本
        ecmaFeatures: { 
          jsx: true,           // 启用JSX语法支持
        },
        sourceType: 'module',   // 使用ES6模块系统
      },
    },
    
    // 自定义规则配置
    rules: {
      // 未使用变量检查规则
      // 错误级别：'error'表示违反规则会报错
      // varsIgnorePattern: 忽略以大写字母或下划线开头的变量（通常是常量或全局变量）
      'no-unused-vars': ['error', { varsIgnorePattern: '^[A-Z_]' }],
    },
  },
])
